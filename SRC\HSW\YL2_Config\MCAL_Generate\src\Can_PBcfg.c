/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Can_PBcfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C"{
#endif

/**
 * @page misra_violations MISRA-C:2012 violations
 */

/*==================================================================================================
 *                                        INCLUDE FILES
==================================================================================================*/
#include "Can.h"
#include "Can_Drv.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CAN_VENDOR_ID_PBCFG_C           (180)
#define CAN_AR_REL_MAJOR_VER_PBCFG_C    (4)
#define CAN_AR_REL_MINOR_VER_PBCFG_C    (4)
#define CAN_AR_REL_REVISION_VER_PBCFG_C (0)
#define CAN_SW_MAJOR_VER_PBCFG_C        (2)
#define CAN_SW_MINOR_VER_PBCFG_C        (2)
#define CAN_SW_PATCH_VER_PBCFG_C        (0)

/*==================================================================================================
*                                         CALLBACKS
==================================================================================================*/



#define CAN_START_SEC_CODE
#include "Can_MemMap.h"
CAN_FUNC __attribute__((weak)) boolean CanReceiveCallOut(uint8 Hrh, Can_IdType CanId, uint8 CanDataLegth, const uint8* CanSduPtr ){(void)Hrh; (void)CanId; (void)CanDataLegth; (void)CanSduPtr; return TRUE;}

CAN_FUNC __attribute__((weak)) void CanBusoff0(void){}
CAN_FUNC __attribute__((weak)) void CanBusoff1(void){}
CAN_FUNC __attribute__((weak)) void CanBusoff2(void){}
CAN_FUNC __attribute__((weak)) void CanBusoff3(void){}


#define CAN_STOP_SEC_CODE
#include "Can_MemMap.h"
/*==================================================================================================
*                                         CONSTANTS
==================================================================================================*/
#define CAN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Can_MemMap.h"
#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)
CAN_CONST const Can_FilterOfExtFifoType ExtRxFifoFltConfig_1[1] =
{
    {
        .FilterCodeExt1 = 0x0U,
        .FilterCodeExt2 = 0x0U,
        .EnhanceRxFifoFilterFormat = CAN_FILTER_FORMAT_ACC_MASK_MODE
    },
};
CAN_CONST const Can_FilterOfExtFifoType ExtRxFifoFltConfig_3[1] =
{
    {
        .FilterCodeExt1 = 0x0U,
        .FilterCodeExt2 = 0x0U,
        .EnhanceRxFifoFilterFormat = CAN_FILTER_FORMAT_ACC_MASK_MODE
    },
};


CAN_CONST const Can_EnhanceRxFifoConfigType Can_EnhanceRxFifoConfig_1 =
{
    .EnhanceRxFifoWatermarkNum = 1U,
    .ExtRxFifoFilterNum = 1U,
    .StdRxFifoFilterNum = 0U,
    .ExtRxFifoFltConfigPtr = ExtRxFifoFltConfig_1,
    .StdRxFifoFltConfigPtr = NULL_PTR,
};


CAN_CONST const Can_EnhanceRxFifoConfigType Can_EnhanceRxFifoConfig_3 =
{
    .EnhanceRxFifoWatermarkNum = 1U,
    .ExtRxFifoFilterNum = 1U,
    .StdRxFifoFilterNum = 0U,
    .ExtRxFifoFltConfigPtr = ExtRxFifoFltConfig_3,
    .StdRxFifoFltConfigPtr = NULL_PTR,
};
#endif/*#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)*/
#if (CAN_LEGACY_FIFO_USAGE == STD_ON)




#endif/*#if (CAN_LEGACY_FIFO_USAGE == STD_ON)*/

#if (CAN_FD_USAGE == STD_ON)
CAN_CONST const CAN_FdBdrConfigType Can_FdBdrConfig_0[1] =
{
    {
        .CanFdBaudrate      = 2000U,
        .CanFdTxBdrSwitch   = TRUE,
        .CanFdPreDiv        = 1U,
        .CanFdPropSeg       = 5U,
        .CanFdSeg1          = 4U,
        .CanFdSeg2          = 2U,
        .CanFdSyncJumpWidth = 1U,
        .CanFdTdcOffset     = 9U
    },
};
CAN_CONST const CAN_FdBdrConfigType Can_FdBdrConfig_1[1] =
{
    {
        .CanFdBaudrate      = 2000U,
        .CanFdTxBdrSwitch   = TRUE,
        .CanFdPreDiv        = 1U,
        .CanFdPropSeg       = 5U,
        .CanFdSeg1          = 4U,
        .CanFdSeg2          = 2U,
        .CanFdSyncJumpWidth = 1U,
        .CanFdTdcOffset     = 9U
    },
};
CAN_CONST const CAN_FdBdrConfigType Can_FdBdrConfig_2[1] =
{
    {
        .CanFdBaudrate      = 2000U,
        .CanFdTxBdrSwitch   = TRUE,
        .CanFdPreDiv        = 1U,
        .CanFdPropSeg       = 5U,
        .CanFdSeg1          = 4U,
        .CanFdSeg2          = 2U,
        .CanFdSyncJumpWidth = 1U,
        .CanFdTdcOffset     = 9U
    },
};
CAN_CONST const CAN_FdBdrConfigType Can_FdBdrConfig_3[1] =
{
    {
        .CanFdBaudrate      = 2000U,
        .CanFdTxBdrSwitch   = TRUE,
        .CanFdPreDiv        = 1U,
        .CanFdPropSeg       = 5U,
        .CanFdSeg1          = 4U,
        .CanFdSeg2          = 2U,
        .CanFdSyncJumpWidth = 1U,
        .CanFdTdcOffset     = 9U
    },
};
#endif



CAN_CONST const Can_BdrConfigType CanControllerBaudrateConfig_0[1] = 
{
    {
        .CanBaudrateConfigID = 0U,
        .CanBaudrate         = 500U,
        .CanPreDiv           = 3U,
        .CanPropSeg          = 7U,
        .CanSeg1             = 5U,
        .CanSeg2             = 3U,
        .CanSyncJumpWidth    = 1U,
#if (CAN_FD_USAGE == STD_ON)
        .CanFdBdrConfig = (const CAN_FdBdrConfigType *)&Can_FdBdrConfig_0[0],
#endif
    },
};


CAN_CONST const Can_BdrConfigType CanControllerBaudrateConfig_1[1] = 
{
    {
        .CanBaudrateConfigID = 0U,
        .CanBaudrate         = 500U,
        .CanPreDiv           = 3U,
        .CanPropSeg          = 7U,
        .CanSeg1             = 5U,
        .CanSeg2             = 3U,
        .CanSyncJumpWidth    = 1U,
#if (CAN_FD_USAGE == STD_ON)
        .CanFdBdrConfig = (const CAN_FdBdrConfigType *)&Can_FdBdrConfig_1[0],
#endif
    },
};


CAN_CONST const Can_BdrConfigType CanControllerBaudrateConfig_2[1] = 
{
    {
        .CanBaudrateConfigID = 0U,
        .CanBaudrate         = 500U,
        .CanPreDiv           = 3U,
        .CanPropSeg          = 7U,
        .CanSeg1             = 5U,
        .CanSeg2             = 3U,
        .CanSyncJumpWidth    = 1U,
#if (CAN_FD_USAGE == STD_ON)
        .CanFdBdrConfig = (const CAN_FdBdrConfigType *)&Can_FdBdrConfig_2[0],
#endif
    },
};


CAN_CONST const Can_BdrConfigType CanControllerBaudrateConfig_3[1] = 
{
    {
        .CanBaudrateConfigID = 0U,
        .CanBaudrate         = 500U,
        .CanPreDiv           = 3U,
        .CanPropSeg          = 7U,
        .CanSeg1             = 5U,
        .CanSeg2             = 3U,
        .CanSyncJumpWidth    = 1U,
#if (CAN_FD_USAGE == STD_ON)
        .CanFdBdrConfig = (const CAN_FdBdrConfigType *)&Can_FdBdrConfig_3[0],
#endif
    },
};

CAN_CONST const Can_MbRegionConfigType MbRegionConfig_0[1] =
{
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
};
CAN_CONST const Can_MbRegionConfigType MbRegionConfig_1[2] =
{
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
};
CAN_CONST const Can_MbRegionConfigType MbRegionConfig_2[1] =
{
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
};
CAN_CONST const Can_MbRegionConfigType MbRegionConfig_3[2] =
{
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
    {
        .MbPlSizeType     = CAN_PLSIZE_64_BYTES,
        .MbMsgBufferNum   = 7U,
        .PayloadSize      = 64U,
        .PayloadRamLength = 72U
    },
};


CAN_CONST const Can_PayloadConfigType    Can_PayloadConfig_0 =
{
    .MbRegionNum     = (Can_HwObjRegionType)1U,
    .MbRegionConfig  = MbRegionConfig_0,
    .ChPayloadMaxNum = 7U,
    .RxFifoType      = CAN_RX_FIFO_NONE,
#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)
    .EnhanceRxFifoConfigPtr = NULL_PTR,	/*!< Point the rx fifo config */
#endif
#if (CAN_LEGACY_FIFO_USAGE == STD_ON)
    .LegacyRxFifoConfigPtr  = NULL_PTR,
#endif
};

CAN_CONST const Can_PayloadConfigType    Can_PayloadConfig_1 =
{
    .MbRegionNum     = (Can_HwObjRegionType)2U,
    .MbRegionConfig  = MbRegionConfig_1,
    .ChPayloadMaxNum = 14U,
    .RxFifoType      = CAN_RX_FIFO_ENHANCE,
#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)
    .EnhanceRxFifoConfigPtr = &Can_EnhanceRxFifoConfig_1,	/*!< Point the rx fifo config */
#endif
#if (CAN_LEGACY_FIFO_USAGE == STD_ON)
    .LegacyRxFifoConfigPtr  = NULL_PTR,
#endif
};

CAN_CONST const Can_PayloadConfigType    Can_PayloadConfig_2 =
{
    .MbRegionNum     = (Can_HwObjRegionType)1U,
    .MbRegionConfig  = MbRegionConfig_2,
    .ChPayloadMaxNum = 7U,
    .RxFifoType      = CAN_RX_FIFO_NONE,
#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)
    .EnhanceRxFifoConfigPtr = NULL_PTR,	/*!< Point the rx fifo config */
#endif
#if (CAN_LEGACY_FIFO_USAGE == STD_ON)
    .LegacyRxFifoConfigPtr  = NULL_PTR,
#endif
};

CAN_CONST const Can_PayloadConfigType    Can_PayloadConfig_3 =
{
    .MbRegionNum     = (Can_HwObjRegionType)2U,
    .MbRegionConfig  = MbRegionConfig_3,
    .ChPayloadMaxNum = 14U,
    .RxFifoType      = CAN_RX_FIFO_ENHANCE,
#if (CAN_ENHANCE_FIFO_USAGE == STD_ON)
    .EnhanceRxFifoConfigPtr = &Can_EnhanceRxFifoConfig_3,	/*!< Point the rx fifo config */
#endif
#if (CAN_LEGACY_FIFO_USAGE == STD_ON)
    .LegacyRxFifoConfigPtr  = NULL_PTR,
#endif
};

CAN_CONST const Can_FilterOfMbType Can_FilterConfig_0[1] =
{
    {
        .FilterCode = 0x0U,
        .MaskCode   = 0x0U,
    }
};
CAN_CONST const Can_FilterOfMbType Can_FilterConfig_4[1] =
{
    {
        .FilterCode = 0x0U,
        .MaskCode   = 0x0U,
    }
};
CAN_CONST const Can_FilterOfMbType Can_FilterConfig_7[1] =
{
    {
        .FilterCode = 0x0U,
        .MaskCode   = 0x0U,
    }
};



CAN_CONST const Can_HohConfigType Can_HohConfig[9] =
{
    {
        .CanObjId          = 0U,
        .CanChannelId      = 0U,
        .CanHwObjRegionId  = CAN_RAM_REGION_0,
        .CanHwObjNum       = 5U,
        .ObjectType        = CAN_RECEIVE,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .MsgIdType         = MIXED,
        .FilterConfig      = Can_FilterConfig_0,
        .CanHwFlagStartId  = 0,
        .CanHwObjStartId = 0
    },
    {
        .CanObjId          = 1U,
        .CanChannelId      = 0U,
        .CanHwObjRegionId  = CAN_RAM_REGION_0,
        .CanHwObjNum       = 2U,
        .ObjectType        = CAN_TRANSMIT,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .CanFdPaddingValue = 0xccU,
#endif
#if (DEVELOPPING == STD_ON)
        .TrigTransEnable   = FALSE,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 5,
        .CanHwObjStartId = 5
    },
    {
        .CanObjId          = 2U,
        .CanChannelId      = 1U,
        .CanHwObjRegionId  = CAN_RX_FIFO_ENHANCE,
        .CanHwObjNum       = 32U,
        .ObjectType        = CAN_RECEIVE,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 255,
        .CanHwObjStartId = 255
    },
    {
        .CanObjId          = 3U,
        .CanChannelId      = 1U,
        .CanHwObjRegionId  = CAN_RAM_REGION_1,
        .CanHwObjNum       = 7U,
        .ObjectType        = CAN_TRANSMIT,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .CanFdPaddingValue = 0xccU,
#endif
#if (DEVELOPPING == STD_ON)
        .TrigTransEnable   = FALSE,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 7,
        .CanHwObjStartId = 0
    },
    {
        .CanObjId          = 4U,
        .CanChannelId      = 2U,
        .CanHwObjRegionId  = CAN_RAM_REGION_0,
        .CanHwObjNum       = 4U,
        .ObjectType        = CAN_RECEIVE,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .MsgIdType         = MIXED,
        .FilterConfig      = Can_FilterConfig_4,
        .CanHwFlagStartId  = 0,
        .CanHwObjStartId = 0
    },
    {
        .CanObjId          = 5U,
        .CanChannelId      = 2U,
        .CanHwObjRegionId  = CAN_RAM_REGION_0,
        .CanHwObjNum       = 3U,
        .ObjectType        = CAN_TRANSMIT,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .CanFdPaddingValue = 0xccU,
#endif
#if (DEVELOPPING == STD_ON)
        .TrigTransEnable   = FALSE,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 4,
        .CanHwObjStartId = 4
    },
    {
        .CanObjId          = 6U,
        .CanChannelId      = 3U,
        .CanHwObjRegionId  = CAN_RX_FIFO_ENHANCE,
        .CanHwObjNum       = 32U,
        .ObjectType        = CAN_RECEIVE,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 255,
        .CanHwObjStartId = 255
    },
    {
        .CanObjId          = 7U,
        .CanChannelId      = 3U,
        .CanHwObjRegionId  = CAN_RAM_REGION_0,
        .CanHwObjNum       = 7U,
        .ObjectType        = CAN_RECEIVE,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .MsgIdType         = MIXED,
        .FilterConfig      = Can_FilterConfig_7,
        .CanHwFlagStartId  = 0,
        .CanHwObjStartId = 0
    },
    {
        .CanObjId          = 8U,
        .CanChannelId      = 3U,
        .CanHwObjRegionId  = CAN_RAM_REGION_1,
        .CanHwObjNum       = 7U,
        .ObjectType        = CAN_TRANSMIT,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) || (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
        .UsePolling        = FALSE,
        .PollingPeriodId   = 255U,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .CanFdPaddingValue = 0xccU,
#endif
#if (DEVELOPPING == STD_ON)
        .TrigTransEnable   = FALSE,
#endif
#if (DEVELOPPING == STD_ON)
        .BasicFullType     = CAN_HANDLE_BASIC,
#endif
        .CanHwFlagStartId  = 7,
        .CanHwObjStartId = 0
    },
};



/**
 * @brief This map is used to translate the 'CAN hardware ID' to 'a logical ID'.
 */
CAN_CONST const uint8 Can_HwIdTable[8] = { 1U, 255U, 3U, 2U, 255U, 0U, 255U, 255U,  };

/**
 * @brief This map is used to convert a mailbox flag ID to the HOH ID and region for CAN0.
 */
CAN_CONST const Can_MbFlagMatrixType Can_HwFlagMatrixConfig_0[7] =
{
    /* Flag id: 0 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 0,
    },
    /* Flag id: 1 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 0,
    },
    /* Flag id: 2 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 0,
    },
    /* Flag id: 3 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 0,
    },
    /* Flag id: 4 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 0,
    },
    /* Flag id: 5 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 1,
    },
    /* Flag id: 6 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 1,
    },
};

/**
 * @brief This map is used to convert a mailbox flag ID to the HOH ID and region for CAN1.
 */
CAN_CONST const Can_MbFlagMatrixType Can_HwFlagMatrixConfig_1[14] =
{
    /* Flag id: 0 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 1 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 2 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 3 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 4 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 5 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 6 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = MB_NOT_USE,
    },
    /* Flag id: 7 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 8 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 9 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 10 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 11 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 12 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
    /* Flag id: 13 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 3,
    },
};

/**
 * @brief This map is used to convert a mailbox flag ID to the HOH ID and region for CAN2.
 */
CAN_CONST const Can_MbFlagMatrixType Can_HwFlagMatrixConfig_2[7] =
{
    /* Flag id: 0 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 4,
    },
    /* Flag id: 1 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 4,
    },
    /* Flag id: 2 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 4,
    },
    /* Flag id: 3 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 4,
    },
    /* Flag id: 4 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 5,
    },
    /* Flag id: 5 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 5,
    },
    /* Flag id: 6 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 5,
    },
};

/**
 * @brief This map is used to convert a mailbox flag ID to the HOH ID and region for CAN3.
 */
CAN_CONST const Can_MbFlagMatrixType Can_HwFlagMatrixConfig_3[14] =
{
    /* Flag id: 0 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 1 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 2 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 3 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 4 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 5 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 6 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_0,
        .CanHohId      = 7,
    },
    /* Flag id: 7 */
    {
        .CanHwObjId    = 0,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 8 */
    {
        .CanHwObjId    = 1,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 9 */
    {
        .CanHwObjId    = 2,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 10 */
    {
        .CanHwObjId    = 3,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 11 */
    {
        .CanHwObjId    = 4,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 12 */
    {
        .CanHwObjId    = 5,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
    /* Flag id: 13 */
    {
        .CanHwObjId    = 6,
        .CanHwRegionId = CAN_RAM_REGION_1,
        .CanHohId      = 8,
    },
};


/**
 * @brief This is callback enable list for CAN0
 */
CAN_CONST const Can_CallbackEntranceType Can_CallbackEntrance_0 =
{
#if ((CAN_LEGACY_FIFO_USAGE == STD_ON) || (CAN_ENHANCE_FIFO_USAGE == STD_ON))
    .ReceiveFifoFullCallback      = NULL_PTR,
    .ReceiveFifoOverflowCallback  = NULL_PTR,
#endif
    .OverrunCallback              = NULL_PTR,
    .WarningCallback              = NULL_PTR,
    .BusoffCallback               = CanBusoff0,
    .ErrorCallback                = NULL_PTR,
    .ErrFastCallback              = NULL_PTR,
    .ErrNceCallback               = NULL_PTR,
    .ErrCeCallback                = NULL_PTR,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
    .WakeupCallback               = NULL_PTR,
#endif
#if (CAN_ICOM_USAGE == STD_ON)
    .IcomCallback                 = NULL_PTR,
#endif
};

/**
 * @brief This is callback enable list for CAN1
 */
CAN_CONST const Can_CallbackEntranceType Can_CallbackEntrance_1 =
{
#if ((CAN_LEGACY_FIFO_USAGE == STD_ON) || (CAN_ENHANCE_FIFO_USAGE == STD_ON))
    .ReceiveFifoFullCallback      = NULL_PTR,
    .ReceiveFifoOverflowCallback  = NULL_PTR,
#endif
    .OverrunCallback              = NULL_PTR,
    .WarningCallback              = NULL_PTR,
    .BusoffCallback               = CanBusoff1,
    .ErrorCallback                = NULL_PTR,
    .ErrFastCallback              = NULL_PTR,
    .ErrNceCallback               = NULL_PTR,
    .ErrCeCallback                = NULL_PTR,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
    .WakeupCallback               = NULL_PTR,
#endif
#if (CAN_ICOM_USAGE == STD_ON)
    .IcomCallback                 = NULL_PTR,
#endif
};

/**
 * @brief This is callback enable list for CAN2
 */
CAN_CONST const Can_CallbackEntranceType Can_CallbackEntrance_2 =
{
#if ((CAN_LEGACY_FIFO_USAGE == STD_ON) || (CAN_ENHANCE_FIFO_USAGE == STD_ON))
    .ReceiveFifoFullCallback      = NULL_PTR,
    .ReceiveFifoOverflowCallback  = NULL_PTR,
#endif
    .OverrunCallback              = NULL_PTR,
    .WarningCallback              = NULL_PTR,
    .BusoffCallback               = CanBusoff2,
    .ErrorCallback                = NULL_PTR,
    .ErrFastCallback              = NULL_PTR,
    .ErrNceCallback               = NULL_PTR,
    .ErrCeCallback                = NULL_PTR,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
    .WakeupCallback               = NULL_PTR,
#endif
#if (CAN_ICOM_USAGE == STD_ON)
    .IcomCallback                 = NULL_PTR,
#endif
};

/**
 * @brief This is callback enable list for CAN3
 */
CAN_CONST const Can_CallbackEntranceType Can_CallbackEntrance_3 =
{
#if ((CAN_LEGACY_FIFO_USAGE == STD_ON) || (CAN_ENHANCE_FIFO_USAGE == STD_ON))
    .ReceiveFifoFullCallback      = NULL_PTR,
    .ReceiveFifoOverflowCallback  = NULL_PTR,
#endif
    .OverrunCallback              = NULL_PTR,
    .WarningCallback              = NULL_PTR,
    .BusoffCallback               = CanBusoff3,
    .ErrorCallback                = NULL_PTR,
    .ErrFastCallback              = NULL_PTR,
    .ErrNceCallback               = NULL_PTR,
    .ErrCeCallback                = NULL_PTR,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
    .WakeupCallback               = NULL_PTR,
#endif
#if (CAN_ICOM_USAGE == STD_ON)
    .IcomCallback                 = NULL_PTR,
#endif
};


CAN_CONST const Can_ChannelConfigType Can_ChannelConfig[4] =
{
    {
        .CanChannelId            = 0U,
        .CanHwChId               = FLEXCAN_5,
        .CanChActivation         = TRUE,
        .ChBaseAddr              = 0x4003E000U,
        .CanTrippleSamp          = FALSE,
        /* Do not need to take care of 'CanAutoBusoffRecovery' param, The driver ensures that
           when a Busoff occurs, the Can controller will always enter the STOPPED state and
           has complied with MCAL requirements */
        .CanAutoBusoffRecovery   = TRUE,
        .CanRxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanTxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanBusoffProcessing     = CAN_PROCESS_INTERRUPT,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
        .CanWakeupProcessing     = CAN_PROCESS_INTERRUPT,
        .CanWakeupSupport        = FALSE,
        .EcuMWakeupSource        = FALSE,
#endif
#if (CAN_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .CanWakeupFunctionalityAPI = FALSE,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .FdUsage                 = TRUE,
        .IsoModeUsage            = TRUE,
#endif
        .CanTimeQuantaSource     = CAN_CLOCK_SOURCE_OSC,
        .ChBaudrateNum           = 1U,
        .DefaultBdrConfig        = &CanControllerBaudrateConfig_0[0],
        .BdrConfigPtr            = CanControllerBaudrateConfig_0,
        .PayloadConfigPtr        = &Can_PayloadConfig_0,
        .CanCallbackPtr          = &Can_CallbackEntrance_0,
        .CanFifoHrhId            = 255U,
        .CanHwFlagMatrixPtr      = Can_HwFlagMatrixConfig_0,
        .RamMbNum                = 32U,
        .RamIrmqEn               = TRUE,
#if (CAN_MEMECC_FEATURE == STD_ON)
        .RamEnhFifoEn            = FALSE,
        .RamHrTimeStmpEn         = TRUE,
        .RamEnhMbMemEn           = FALSE,
#endif
    },
    {
        .CanChannelId            = 1U,
        .CanHwChId               = FLEXCAN_0,
        .CanChActivation         = TRUE,
        .ChBaseAddr              = 0x40030000U,
        .CanTrippleSamp          = FALSE,
        /* Do not need to take care of 'CanAutoBusoffRecovery' param, The driver ensures that
           when a Busoff occurs, the Can controller will always enter the STOPPED state and
           has complied with MCAL requirements */
        .CanAutoBusoffRecovery   = TRUE,
        .CanRxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanTxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanBusoffProcessing     = CAN_PROCESS_INTERRUPT,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
        .CanWakeupProcessing     = CAN_PROCESS_INTERRUPT,
        .CanWakeupSupport        = FALSE,
        .EcuMWakeupSource        = FALSE,
#endif
#if (CAN_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .CanWakeupFunctionalityAPI = FALSE,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .FdUsage                 = TRUE,
        .IsoModeUsage            = TRUE,
#endif
        .CanTimeQuantaSource     = CAN_CLOCK_SOURCE_OSC,
        .ChBaudrateNum           = 1U,
        .DefaultBdrConfig        = &CanControllerBaudrateConfig_1[0],
        .BdrConfigPtr            = CanControllerBaudrateConfig_1,
        .PayloadConfigPtr        = &Can_PayloadConfig_1,
        .CanCallbackPtr          = &Can_CallbackEntrance_1,
        .CanFifoHrhId            = 2U,
        .CanHwFlagMatrixPtr      = Can_HwFlagMatrixConfig_1,
        .RamMbNum                = 64U,
        .RamIrmqEn               = TRUE,
#if (CAN_MEMECC_FEATURE == STD_ON)
        .RamEnhFifoEn            = TRUE,
        .RamHrTimeStmpEn         = TRUE,
        .RamEnhMbMemEn           = FALSE,
#endif
    },
    {
        .CanChannelId            = 2U,
        .CanHwChId               = FLEXCAN_3,
        .CanChActivation         = TRUE,
        .ChBaseAddr              = 0x4003C000U,
        .CanTrippleSamp          = FALSE,
        /* Do not need to take care of 'CanAutoBusoffRecovery' param, The driver ensures that
           when a Busoff occurs, the Can controller will always enter the STOPPED state and
           has complied with MCAL requirements */
        .CanAutoBusoffRecovery   = TRUE,
        .CanRxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanTxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanBusoffProcessing     = CAN_PROCESS_INTERRUPT,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
        .CanWakeupProcessing     = CAN_PROCESS_INTERRUPT,
        .CanWakeupSupport        = FALSE,
        .EcuMWakeupSource        = FALSE,
#endif
#if (CAN_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .CanWakeupFunctionalityAPI = FALSE,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .FdUsage                 = TRUE,
        .IsoModeUsage            = TRUE,
#endif
        .CanTimeQuantaSource     = CAN_CLOCK_SOURCE_OSC,
        .ChBaudrateNum           = 1U,
        .DefaultBdrConfig        = &CanControllerBaudrateConfig_2[0],
        .BdrConfigPtr            = CanControllerBaudrateConfig_2,
        .PayloadConfigPtr        = &Can_PayloadConfig_2,
        .CanCallbackPtr          = &Can_CallbackEntrance_2,
        .CanFifoHrhId            = 255U,
        .CanHwFlagMatrixPtr      = Can_HwFlagMatrixConfig_2,
        .RamMbNum                = 32U,
        .RamIrmqEn               = TRUE,
#if (CAN_MEMECC_FEATURE == STD_ON)
        .RamEnhFifoEn            = FALSE,
        .RamHrTimeStmpEn         = TRUE,
        .RamEnhMbMemEn           = FALSE,
#endif
    },
    {
        .CanChannelId            = 3U,
        .CanHwChId               = FLEXCAN_2,
        .CanChActivation         = TRUE,
        .ChBaseAddr              = 0x40038000U,
        .CanTrippleSamp          = FALSE,
        /* Do not need to take care of 'CanAutoBusoffRecovery' param, The driver ensures that
           when a Busoff occurs, the Can controller will always enter the STOPPED state and
           has complied with MCAL requirements */
        .CanAutoBusoffRecovery   = TRUE,
        .CanRxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanTxProcessing         = CAN_PROCESS_INTERRUPT,
        .CanBusoffProcessing     = CAN_PROCESS_INTERRUPT,
#if (CAN_WAKEUP_SUPPORT == STD_ON)
        .CanWakeupProcessing     = CAN_PROCESS_INTERRUPT,
        .CanWakeupSupport        = FALSE,
        .EcuMWakeupSource        = FALSE,
#endif
#if (CAN_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .CanWakeupFunctionalityAPI = FALSE,
#endif
#if (CAN_FD_USAGE == STD_ON)
        .FdUsage                 = TRUE,
        .IsoModeUsage            = TRUE,
#endif
        .CanTimeQuantaSource     = CAN_CLOCK_SOURCE_OSC,
        .ChBaudrateNum           = 1U,
        .DefaultBdrConfig        = &CanControllerBaudrateConfig_3[0],
        .BdrConfigPtr            = CanControllerBaudrateConfig_3,
        .PayloadConfigPtr        = &Can_PayloadConfig_3,
        .CanCallbackPtr          = &Can_CallbackEntrance_3,
        .CanFifoHrhId            = 6U,
        .CanHwFlagMatrixPtr      = Can_HwFlagMatrixConfig_3,
        .RamMbNum                = 64U,
        .RamIrmqEn               = TRUE,
#if (CAN_MEMECC_FEATURE == STD_ON)
        .RamEnhFifoEn            = TRUE,
        .RamHrTimeStmpEn         = TRUE,
        .RamEnhMbMemEn           = FALSE,
#endif
    },
};

/**
 * @brief CAN config
 */
CAN_CONST const Can_ConfigType Can_Config =
{
    .CanChannelNum        = 4U,
    .CanHohNum            = 9U,
    .CanChCfgPtr          = Can_ChannelConfig,
    .CanHohCfgPtr         = Can_HohConfig,
    .CanHwIdTable         = Can_HwIdTable,
#if ((CAN_RX_PROCESS_HAS_POLLING == STD_ON) && (CAN_TX_PROCESS_HAS_POLLING == STD_ON))
    .CanPolPeriodNum      = 0,
#endif
#if (CAN_RX_PROCESS_HAS_POLLING == STD_ON)
    .CanHrhPolPeriodPtr   = NULL_PTR,
#endif
#if (CAN_TX_PROCESS_HAS_POLLING == STD_ON)
    .CanHthPolPeriodPtr   = NULL_PTR,
#endif
    .CanReceiveCallback   = CanReceiveCallOut,
    .CanTransmitCallback  = NULL_PTR,
#if ((CAN_ENHANCE_FIFO_USAGE == STD_ON) || (CAN_LEGACY_FIFO_USAGE == STD_ON))
#if (CAN_FIFO_IDHIT_USAGE == STD_ON)
    .CanIdhitCallback     = NULL_PTR,
#endif
#endif
#if (CAN_ICOM_USAGE == STD_ON)
    .CanIcomNum           = 0U,
    .CanIcomCfgPtr        = NULL_PTR,
#endif
};

#define CAN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Can_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

