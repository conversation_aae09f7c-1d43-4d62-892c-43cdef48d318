/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file system.c
 * @brief 
 * 
 */


#include "pSIP_Efm.h"
#include "pSIP_Wdg.h"
#include "YTM32B1Mx_CM33_DSP_FP.h"
#include "Mpu_Lld_Cfg.h"
#include "Mpu_Lld_M33.h"

void SystemInit(void)
{
    /* Enable CP10 and CP11 coprocessors */
    SCB->CPACR |= (3UL << 20 | 3UL << 22);    
    /* Enable the Flash arrary deep powerdown mode and Flash data prefetch. */
    EFM->CTRL |= EFM_CTRL_DPD_EN_MASK | EFM_CTRL_PREFETCH_EN_MASK;
    /* Disable the WDG. */
    WDG->SVCR = 0xB631;
    WDG->SVCR = 0xC278;
    WDG->CR &= ~WDG_CR_EN_MASK; 
    /* Enable the MPU */
    Mpu_Lld_M33_Init(&MpuConfig);
}

