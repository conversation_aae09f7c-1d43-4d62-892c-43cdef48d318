/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */

#include "CddI2c.h"
#include "CddI2c_PBcfg.h"

#if (I2C_DMA_USED == STD_ON )
#include "CddDma_Cfg.h"
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CDDI2C_VENDOR_ID_PBCFG_C               (180)
#define CDDI2C_AR_REL_MAJOR_VER_PBCFG_C        (4)
#define CDDI2C_AR_REL_MINOR_VER_PBCFG_C        (4)
#define CDDI2C_AR_REL_REVISION_VER_PBCFG_C     (0)
#define CDDI2C_SW_MAJOR_VER_PBCFG_C            (2)
#define CDDI2C_SW_MINOR_VER_PBCFG_C            (2)
#define CDDI2C_SW_PATCH_VER_PBCFG_C            (0)

/*==================================================================================================
                                            FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and CDDI2C header file are of the same vendor */
#if (CDDI2C_VENDOR_ID_PBCFG_C != CDDI2C_VENDOR_ID_PBCFG)
#error "CddI2c_PBcfg.c and CddI2c_PBcfg.h have different vendor ids"
#endif

/* Check if source file and CDDI2C header file are of the same Autosar version */
#if ((CDDI2C_AR_REL_MAJOR_VER_PBCFG_C != CDDI2C_AR_REL_MAJOR_VER_PBCFG) || \
     (CDDI2C_AR_REL_MINOR_VER_PBCFG_C != CDDI2C_AR_REL_MINOR_VER_PBCFG) || \
     (CDDI2C_AR_REL_REVISION_VER_PBCFG_C != CDDI2C_AR_REL_REVISION_VER_PBCFG))
#error "AutoSar Version Numbers of CddI2c_PBcfg.c and CddI2c_PBcfg.h are different"
#endif

/* Check if source file and CDDI2C header file are of the same Software version */
#if ((CDDI2C_SW_MAJOR_VER_PBCFG_C != CDDI2C_SW_MAJOR_VER_PBCFG) || \
     (CDDI2C_SW_MINOR_VER_PBCFG_C != CDDI2C_SW_MINOR_VER_PBCFG) || \
     (CDDI2C_SW_PATCH_VER_PBCFG_C != CDDI2C_SW_PATCH_VER_PBCFG))
#error "Software Version Numbers of CddI2c_PBcfg.c and CddI2c_PBcfg.h are different"
#endif

/*==================================================================================================
 *                                       Function Prototypes
==================================================================================================*/

/*================================================================================================== */

/**
* @brief   Total number of I2c channel configured.
*/
#define CDD_I2C_HW_CONFIG_NUM        (1U)
/*================================================================================================== */

#define CDDI2C_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CddI2c_MemMap.h"

CDDI2C_CONST static const I2C_BaudRateType I2cChannel_0_BaudrateParame =
{       
    .Prescaler = I2C_MASTER_PRESC_DIV_1,
    .ClkHi = 22U,
    .ClkLo = 22U,
    .SetHold = 22U,
    .DataValid = 10U,
};
CDDI2C_CONST static const I2C_MasterConfigType  I2cChannel_0_MaterConfig =
{
    /* Slave address */
    .SlaveAddress = 0x00U,
    /* 10-bit address*/
    .Is10bitAddr =  FALSE,
    /* Operating Mode */
    .OperatingMode = I2C_STANDARD_MODE,
    /* Clock source */
    .ModuleWorkingClock =  4800000,
    /* Baudrate parameters */
    .BaudrateParam = &I2cChannel_0_BaudrateParame,
    /* Pin Low Timeout  */
    .PinLowTimeout = 0U,
    /* Bus Idle Timeout */
    .BusIdleTimeout = 0U,
    /* Digtal Filter SDA */
    .DigitalFilterSDA = 0U,
    /* Digtal Filter SCL */
    .DigitalFilterSCL = 0U,
    /* Transfer Type in async mode */
    .TransferType = I2C_USING_INTERRUPTS,
#if (I2C_DMA_USED == STD_ON )
#endif /*(I2C_DMA_USED == STD_ON )*/
    /* Master Callback */
    .MasterCallback = NULL_PTR,
    /* Master Callback Parameter */
    .CallbackParam = 0U,
#if (CDDI2C_SYNC_TRANSFER_TIMEOUT_EN == STD_ON)
    /* Specifies the maximum of loops for blocking function unitl a timeout is raised in short term wait loops.*/
    .TimeoutValue = 4294967295U,
#endif
};


CDDI2C_CONST static const CddI2c_HwChannelConfigType CddI2c_ChannelConfig[CDD_I2C_HW_CONFIG_NUM] =
{
    /*I2c channel 0 configuration data*/
    {
        .I2cHwUnit = 0U,
        .TransferModeConfig = I2C_MASTER_MODE,
        .HwChannelConfig = 
        {
            .Master = &I2cChannel_0_MaterConfig,
            .Slave = NULL_PTR,
        },
    },
};

CDDI2C_CONST const CddI2c_ConfigType CddI2c_Config =
{
    .I2cInitChnAmount = CDD_I2C_HW_CONFIG_NUM,
    .I2cChnConfig = &CddI2c_ChannelConfig[0],
};

#define CDDI2C_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CddI2c_MemMap.h"

