/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file IntrCtrl_Lld_Cfg.c
 * @brief 
 * 
 */



#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/
#include "IntCtrl_Lld_Cfg.h"
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PLATFORM_INTCTRL_LLD_CFG_VENDOR_ID_C                          (180)
#define PLATFORM_INTCTRL_LLD_CFG_SW_MAJOR_VERSION_C                   (2)
#define PLATFORM_INTCTRL_LLD_CFG_SW_MINOR_VERSION_C                   (2)
#define PLATFORM_INTCTRL_LLD_CFG_SW_PATCH_VERSION_C                   (0)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and IntCtrl_Lld_Cfg header file are of the same vendor */
#if (PLATFORM_INTCTRL_LLD_CFG_VENDOR_ID_C != PLATFORM_INTCTRL_LLD_CFG_VENDOR_ID)
    #error "IntCtrl_Lld_Cfg.c and IntCtrl_Lld_Cfg.h have different vendor ids"
#endif

/* Check if current file and IntCtrl_Lld_Cfg header file are of the same Software version */
#if ((PLATFORM_INTCTRL_LLD_CFG_SW_MAJOR_VERSION_C != PLATFORM_INTCTRL_LLD_CFG_SW_MAJOR_VERSION) || \
     (PLATFORM_INTCTRL_LLD_CFG_SW_MINOR_VERSION_C != PLATFORM_INTCTRL_LLD_CFG_SW_MINOR_VERSION) || \
     (PLATFORM_INTCTRL_LLD_CFG_SW_PATCH_VERSION_C != PLATFORM_INTCTRL_LLD_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of IntCtrl_Lld_Cfg.c and IntCtrl_Lld_Cfg.h are different"
#endif
/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define PLATFORM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Platform_MemMap.h"

/* List of configurations for interrupts */
PLATFORM_CONST static const IntCtrl_Lld_IrqConfigType IrqConfig[] = {
    {   .IrqNumber    = DMA0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = DMA1_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = DMA2_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = DMA3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA4_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA6_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA8_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA9_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA10_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA11_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA12_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA13_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA14_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA15_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = DMA_Error_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = FPU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = EFM_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = EFM_Error_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = PCU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = EFM_Ecc_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = WDG_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = RCU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = I2C0_Master_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = I2C0_Slave_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SPI0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = SPI1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SPI2_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = I2C1_Master_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = I2C1_Slave_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = LINFlexD0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = LINFlexD1_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = LINFlexD2_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = ADC0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = ADC1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = ACMP0_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = EMU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = RTC_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = RTC_Seconds_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = pTMR_Ch0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = pTMR_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = pTMR_Ch2_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = pTMR_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = PTU0_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SCU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = lpTMR0_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = GPIOA_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = GPIOB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 1,   
    },
    {   .IrqNumber    = GPIOC_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = GPIOD_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = GPIOE_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 1,   
    },
    {   .IrqNumber    = PTU1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN0_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_Error_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_ORed_32_47_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN0_ORed_48_63_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN1_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_Error_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_ORed_32_47_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN1_ORed_48_63_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN2_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_Error_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_ORed_32_47_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN2_ORed_48_63_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = eTMR0_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR0_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR0_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR0_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR0_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR0_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR1_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR2_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR3_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR4_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR5_Ch0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 1,   
    },
    {   .IrqNumber    = eTMR5_Ch2_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR5_Ch4_Ch5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR5_Ch6_Ch7_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR5_Fault_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = eTMR5_Ovf_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = TRNG_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = HCU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = INTM_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = TMR0_Ch0_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = TMR0_Ch1_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = TMR0_Ch2_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = TMR0_Ch3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = LINFlexD3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = LINFlexD4_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = LINFlexD5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = I2C2_Master_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = I2C2_Slave_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SPI3_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SPI4_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = SPI5_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN3_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN3_Error_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN3_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN3_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN3_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN4_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN4_Error_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN4_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN4_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN4_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
    {   .IrqNumber    = CAN5_ORed_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN5_Error_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN5_Wake_Up_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN5_ORed_0_15_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = CAN5_ORed_16_31_MB_IRQn,
        .IrqEnabled   = (boolean)STD_ON,
        .IrqPriority = 2,   
    },
    {   .IrqNumber    = WKU_IRQn,
        .IrqEnabled   = (boolean)STD_OFF,
        .IrqPriority = 0,   
    },
};

/* Configuration structure for interrupt controller */
PLATFORM_CONST const IntCtrl_Lld_CtrlConfigType IntCtrlConfig = {
    .ConfigIrqCount = 141U,
#if (INTCTRL_LLD_ENABLE_VTOR_CONFIG == STD_ON)
    .VectorTableAddress = 0x1fff0100U,
#endif
    .IrqConfig        = IrqConfig  
};


#define PLATFORM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Platform_MemMap.h"

#ifdef __cplusplus
}
#endif

