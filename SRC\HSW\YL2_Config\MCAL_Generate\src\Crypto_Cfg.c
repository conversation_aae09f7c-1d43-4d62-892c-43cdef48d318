/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */

/*==================================================================================================
*                                          INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Std_Types.h"
#include "Crypto_Ip_Types.h"
#include "Crypto_Types.h"
#include "Crypto.h"
#include "Crypto_Cfg.h"
#if(CRYPTO_ASYNJOB_METHOD_SUPPORT == CRYPTO_METHOD_DMA)
#include "CddDma_Cfg.h"
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/


/*==================================================================================================*/
/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/
#define CRYPTO_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Crypto_MemMap.h"
/* Array storing the Crypto primitives in the __CryptoDriverObject_MAC__*/
CRYPTO_CONST static const Crypto_PrimitiveType Crypto_Primitives_CryptoDriverObject_MAC[2] =
{
    {
      (Crypto_PrimitiveServiceType)CRYPTO_MACGENERATE,
      (uint8)CRYPTO_ALGOFAM_AES,
      (uint8)CRYPTO_ALGOMODE_CMAC,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
    {
      (Crypto_PrimitiveServiceType)CRYPTO_MACVERIFY,
      (uint8)CRYPTO_ALGOFAM_AES,
      (uint8)CRYPTO_ALGOMODE_CMAC,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
};
/* Array storing the Crypto primitives in the __CryptoDriverObject_Random__*/
CRYPTO_CONST static const Crypto_PrimitiveType Crypto_Primitives_CryptoDriverObject_Random[1] =
{
    {
      (Crypto_PrimitiveServiceType)CRYPTO_RANDOMGENERATE,
      (uint8)CRYPTO_ALGOFAM_RNG,
      (uint8)CRYPTO_ALGOMODE_NOT_SET,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
};
/* Array storing the Crypto primitives in the __CryptoDriverObject_Hash__*/
CRYPTO_CONST static const Crypto_PrimitiveType Crypto_Primitives_CryptoDriverObject_Hash[2] =
{
    {
      (Crypto_PrimitiveServiceType)CRYPTO_HASH,
      (uint8)CRYPTO_ALGOFAM_SHA2_256,
      (uint8)CRYPTO_ALGOMODE_NOT_SET,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
    {
      (Crypto_PrimitiveServiceType)CRYPTO_HASH,
      (uint8)CRYPTO_ALGOFAM_SHA2_384,
      (uint8)CRYPTO_ALGOMODE_NOT_SET,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
};
/* Array storing the Crypto primitives in the __CryptoDriverObject_DEncrypt__*/
CRYPTO_CONST static const Crypto_PrimitiveType Crypto_Primitives_CryptoDriverObject_DEncrypt[2] =
{
    {
      (Crypto_PrimitiveServiceType)CRYPTO_DECRYPT,
      (uint8)CRYPTO_ALGOFAM_AES,
      (uint8)CRYPTO_ALGOMODE_ECB,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
    {
      (Crypto_PrimitiveServiceType)CRYPTO_ENCRYPT,
      (uint8)CRYPTO_ALGOFAM_AES,
      (uint8)CRYPTO_ALGOMODE_ECB,
      (uint8)CRYPTO_ALGOFAM_NOT_SET
    },
};
/* Array storing the information about Crypto Driver Objects*/
CRYPTO_CONST const Crypto_ObjectType  Crypto_ObjectsList[CRYPTO_DRIVER_ObjectMax] =
{
  {
    /* Jobs queue size */
    0U,
    /* Reference to the Crypto primitives list */
    Crypto_Primitives_CryptoDriverObject_MAC,
    /* Number of crypto primitives */
    2U
  },
  {
    /* Jobs queue size */
    0U,
    /* Reference to the Crypto primitives list */
    Crypto_Primitives_CryptoDriverObject_Random,
    /* Number of crypto primitives */
    1U
  },
  {
    /* Jobs queue size */
    0U,
    /* Reference to the Crypto primitives list */
    Crypto_Primitives_CryptoDriverObject_Hash,
    /* Number of crypto primitives */
    2U
  },
  {
    /* Jobs queue size */
    0U,
    /* Reference to the Crypto primitives list */
    Crypto_Primitives_CryptoDriverObject_DEncrypt,
    /* Number of crypto primitives */
    2U
  },
};
/* Array storing keyelements*/
CRYPTO_CONST const Crypto_KeyElementType Crypto_KeyElementList[4] =
{
  {/*CryptoKeyElement_AES_SEC*/
     /* KeyElementId */
     1U,
     /* Allow partial access */
    (boolean)FALSE,
    /* Key element format */
    CRYPTO_KE_FORMAT_BIN_OCTET,
     /* Key element persistent */
    (boolean)FALSE,
     /* Read access type */
    CRYPTO_RA_ENCRYPTED,
    /* Key element max size in bytes*/
    16U,
    /* Write access type */
    CRYPTO_WA_ENCRYPTED,
    /* Flag if usage the SecNVR key */
    (boolean)TRUE,
    /* Identifier of the SecNVR key */
    HCU_KEY_Slot0,
    /*Key length in bits*/
    Crypto_Key_128,
    /* Pointer to Ram storing the Key Element value */
    NULL_PTR,
  },
  {/*CryptoKeyElement_AES_Soft*/
     /* KeyElementId */
     1U,
     /* Allow partial access */
    (boolean)FALSE,
    /* Key element format */
    CRYPTO_KE_FORMAT_BIN_OCTET,
     /* Key element persistent */
    (boolean)TRUE,
     /* Read access type */
    CRYPTO_RA_ENCRYPTED,
    /* Key element max size in bytes*/
    16U,
    /* Write access type */
    CRYPTO_WA_ENCRYPTED,
    /* Flag if usage the SecNVR key */
    (boolean)FALSE,
    /* Identifier of the SecNVR key */
    HCU_KEY_NULL,
    /*Key length in bits*/
    Crypto_Key_128,
    /* Pointer to Ram storing the Key Element value */
    KeyAdress_AES_Soft,
  },
  {/*CryptoKeyElement_Cmac_SEC*/
     /* KeyElementId */
     1U,
     /* Allow partial access */
    (boolean)FALSE,
    /* Key element format */
    CRYPTO_KE_FORMAT_BIN_OCTET,
     /* Key element persistent */
    (boolean)FALSE,
     /* Read access type */
    CRYPTO_RA_ENCRYPTED,
    /* Key element max size in bytes*/
    16U,
    /* Write access type */
    CRYPTO_WA_ENCRYPTED,
    /* Flag if usage the SecNVR key */
    (boolean)TRUE,
    /* Identifier of the SecNVR key */
    HCU_KEY_Slot1,
    /*Key length in bits*/
    Crypto_Key_128,
    /* Pointer to Ram storing the Key Element value */
    NULL_PTR,
  },
  {/*CryptoKeyElement_Cmac_Soft*/
     /* KeyElementId */
     3U,
     /* Allow partial access */
    (boolean)FALSE,
    /* Key element format */
    CRYPTO_KE_FORMAT_BIN_OCTET,
     /* Key element persistent */
    (boolean)TRUE,
     /* Read access type */
    CRYPTO_RA_ENCRYPTED,
    /* Key element max size in bytes*/
    16U,
    /* Write access type */
    CRYPTO_WA_ENCRYPTED,
    /* Flag if usage the SecNVR key */
    (boolean)FALSE,
    /* Identifier of the SecNVR key */
    HCU_KEY_NULL,
    /*Key length in bits*/
    Crypto_Key_128,
    /* Pointer to Ram storing the Key Element value */
    KeyAdress_Cmac_Soft,
  },
};
/* Array of indexes referred by _CryptoKeyType_AES_Hard_ */
CRYPTO_CONST static const uint32 Crypto_KeyRefeIndex_CryptoKeyType_AES_Hard[1] =
{
    0U,
};
/* Array of indexes referred by _CryptoKeyType_AES_Soft_ */
CRYPTO_CONST static const uint32 Crypto_KeyRefeIndex_CryptoKeyType_AES_Soft[1] =
{
    1U,
};
/* Array of indexes referred by _CryptoKeyType_CMAC_Hard_ */
CRYPTO_CONST static const uint32 Crypto_KeyRefeIndex_CryptoKeyType_CMAC_Hard[1] =
{
    2U,
};
/* Array of indexes referred by _CryptoKeyType_CMAC_Soft_ */
CRYPTO_CONST static const uint32 Crypto_KeyRefeIndex_CryptoKeyType_CMAC_Soft[1] =
{
    3U,
};
/* Array storing keys*/
CRYPTO_CONST const Crypto_KeyType Crypto_KeyList[CRYPTO_DRIVER_KeyIDMax] =
{
  {
    /*Number of key elements*/
    1U,
    /*Reference to the list of key elements*/
    Crypto_KeyRefeIndex_CryptoKeyType_AES_Hard,
  },
  {
    /*Number of key elements*/
    1U,
    /*Reference to the list of key elements*/
    Crypto_KeyRefeIndex_CryptoKeyType_AES_Soft,
  },
  {
    /*Number of key elements*/
    1U,
    /*Reference to the list of key elements*/
    Crypto_KeyRefeIndex_CryptoKeyType_CMAC_Hard,
  },
  {
    /*Number of key elements*/
    1U,
    /*Reference to the list of key elements*/
    Crypto_KeyRefeIndex_CryptoKeyType_CMAC_Soft,
  },
};
/*configure Hcu */
CRYPTO_CONST const Crypto_ConfigType Crypto_ProConfig =
{
  MODE_SWAPPING_NO,/*hcu_swapping*/
  HCU_USING_POLLING,/*hcu_carry*/
  /*!< Channel number for Inputdata DMA channel */
  Crypto_DMA_UNUSED,
  /*!< Channel number for Outputdata DMA channel */
  Crypto_DMA_UNUSED
};
#define CRYPTO_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Crypto_MemMap.h"

