/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Port_PBcfg.c
 * @brief 
 * 
 */



#include "Port.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PORT_VENDOR_ID_PBCFG_C               (180)
#define PORT_AR_REL_MAJOR_VER_PBCFG_C        (4)
#define PORT_AR_REL_MINOR_VER_PBCFG_C        (4)
#define PORT_AR_REL_REVISION_VER_PBCFG_C     (0)
#define PORT_SW_MAJOR_VER_PBCFG_C            (2)
#define PORTU_SW_MINOR_VER_PBCFG_C           (2)
#define PORTU_SW_PATCH_VER_PBCFG_C           (0)

/*================================================================================================== */

#define PORT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"

PORT_CONST const static Port_PinConfigType Port_A_PinsConfig[] = {
    /* PCR-0, Feature-ADC0_SE0_ACMP0_IN0 */
    
    {
        .HwPinId = 0,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-1, Feature-ADC0_SE1_ACMP0_IN1 */
    
    {
        .HwPinId = 1,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-2, Feature-I2C0_SDA */
    
    {
        .HwPinId = 2,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_ALT3,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-3, Feature-I2C0_SCL */
    
    {
        .HwPinId = 3,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_ALT3,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-6, Feature-GPIO */
    
    {
        .HwPinId = 6,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-7, Feature-GPIO */
    
    {
        .HwPinId = 7,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-8, Feature-LINFlexD2_RX */
    
    {
        .HwPinId = 8,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-9, Feature-LINFlexD2_TX */
    
    {
        .HwPinId = 9,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-10, Feature-GPIO */
    
    {
        .HwPinId = 10,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-11, Feature-GPIO */
    
    {
        .HwPinId = 11,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-12, Feature-GPIO */
    
    {
        .HwPinId = 12,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-13, Feature-GPIO */
    
    {
        .HwPinId = 13,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-14, Feature-GPIO */
    
    {
        .HwPinId = 14,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-15, Feature-GPIO */
    
    {
        .HwPinId = 15,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-16, Feature-GPIO */
    
    {
        .HwPinId = 16,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-17, Feature-GPIO */
    
    {
        .HwPinId = 17,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-25, Feature-eTMR5_CH0 */
    
    {
        .HwPinId = 25,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-26, Feature-SPI0_PCS0 */
    
    {
        .HwPinId = 26,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_ALT4,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLUP,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-27, Feature-GPIO */
    
    {
        .HwPinId = 27,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-28, Feature-GPIO */
    
    {
        .HwPinId = 28,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-29, Feature-GPIO */
    
    {
        .HwPinId = 29,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-30, Feature-SPI0_SOUT */
    
    {
        .HwPinId = 30,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT4,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-31, Feature-GPIO */
    
    {
        .HwPinId = 31,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
};


PORT_CONST const static Port_PinConfigType Port_B_PinsConfig[] = {
    /* PCR-32, Feature-LINFlexD0_RX */
    
    {
        .HwPinId = 0,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-33, Feature-LINFlexD0_TX */
    
    {
        .HwPinId = 1,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-34, Feature-GPIO */
    
    {
        .HwPinId = 2,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLDOWN,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)1,
    },
    /* PCR-35, Feature-GPIO */
    
    {
        .HwPinId = 3,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLDOWN,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)1,
    },
    /* PCR-36, Feature-GPIO */
    
    {
        .HwPinId = 4,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLDOWN,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-37, Feature-GPIO */
    
    {
        .HwPinId = 5,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLDOWN,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-38, Feature-GPIO */
    
    {
        .HwPinId = 6,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-39, Feature-GPIO */
    
    {
        .HwPinId = 7,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-40, Feature-eTMR3_CH0 */
    
    {
        .HwPinId = 8,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-41, Feature-eTMR3_CH1 */
    
    {
        .HwPinId = 9,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-42, Feature-eTMR3_CH2 */
    
    {
        .HwPinId = 10,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-43, Feature-GPIO */
    
    {
        .HwPinId = 11,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-44, Feature-GPIO */
    
    {
        .HwPinId = 12,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-45, Feature-GPIO */
    
    {
        .HwPinId = 13,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-46, Feature-GPIO */
    
    {
        .HwPinId = 14,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-47, Feature-GPIO */
    
    {
        .HwPinId = 15,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-48, Feature-GPIO */
    
    {
        .HwPinId = 16,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-49, Feature-GPIO */
    
    {
        .HwPinId = 17,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-50, Feature-ADC0_SE16 */
    
    {
        .HwPinId = 18,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-52, Feature-ADC0_SE17 */
    
    {
        .HwPinId = 20,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-53, Feature-ADC0_SE18 */
    
    {
        .HwPinId = 21,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-54, Feature-ADC0_SE19 */
    
    {
        .HwPinId = 22,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-55, Feature-ADC0_SE20 */
    
    {
        .HwPinId = 23,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-57, Feature-GPIO */
    
    {
        .HwPinId = 25,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-59, Feature-GPIO */
    
    {
        .HwPinId = 27,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-60, Feature-GPIO */
    
    {
        .HwPinId = 28,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    /* PCR-61, Feature-GPIO */
    
    {
        .HwPinId = 29,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
};

PORT_CONST const static Port_PinConfigType Port_C_PinsConfig[] = {
    
    /* PCR-64, Feature-CAN3_RX */
    {
        .HwPinId = 0,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT4,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-65, Feature-CAN3_TX */
    {
        .HwPinId = 1,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT4,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-66, Feature-GPIO */
    {
        .HwPinId = 2,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-67, Feature-GPIO */
    {
        .HwPinId = 3,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-69, Feature-GPIO */
    {
        .HwPinId = 5,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-70, Feature-GPIO */
    {
        .HwPinId = 6,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-71, Feature-GPIO */
    {
        .HwPinId = 7,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-72, Feature-LINFlexD1_RX */
    {
        .HwPinId = 8,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-73, Feature-LINFlexD1_TX */
    {
        .HwPinId = 9,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-74, Feature-GPIO */
    {
        .HwPinId = 10,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-75, Feature-GPIO */
    {
        .HwPinId = 11,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-76, Feature-GPIO */
    {
        .HwPinId = 12,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-77, Feature-GPIO */
    {
        .HwPinId = 13,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-78, Feature-GPIO */
    {
        .HwPinId = 14,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-79, Feature-GPIO */
    {
        .HwPinId = 15,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-80, Feature-CAN2_RX */
    {
        .HwPinId = 16,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT3,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-81, Feature-CAN2_TX */
    {
        .HwPinId = 17,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT3,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-83, Feature-GPIO */
    {
        .HwPinId = 19,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-87, Feature-SPI0_SCK */
    {
        .HwPinId = 23,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-91, Feature-GPIO */
    {
        .HwPinId = 27,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-92, Feature-eTMR4_CH7 */
    {
        .HwPinId = 28,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-93, Feature-GPIO */
    {
        .HwPinId = 29,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-94, Feature-GPIO */
    {
        .HwPinId = 30,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-95, Feature-GPIO */
    {
        .HwPinId = 31,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
};


PORT_CONST const static Port_PinConfigType Port_D_PinsConfig[] = {
    
    /* PCR-98, Feature-CAN5_RX */
    {
        .HwPinId = 2,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT5,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLUP,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-99, Feature-CAN5_TX */
    {
        .HwPinId = 3,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT5,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-100, Feature-GPIO */
    {
        .HwPinId = 4,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-101, Feature-GPIO */
    {
        .HwPinId = 5,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-102, Feature-GPIO */
    {
        .HwPinId = 6,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-103, Feature-GPIO */
    {
        .HwPinId = 7,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-104, Feature-GPIO */
    {
        .HwPinId = 8,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-105, Feature-GPIO */
    {
        .HwPinId = 9,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-106, Feature-GPIO */
    {
        .HwPinId = 10,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-107, Feature-GPIO */
    {
        .HwPinId = 11,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-108, Feature-GPIO */
    {
        .HwPinId = 12,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-109, Feature-GPIO */
    {
        .HwPinId = 13,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-110, Feature-GPIO */
    {
        .HwPinId = 14,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-111, Feature-GPIO */
    {
        .HwPinId = 15,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-112, Feature-GPIO */
    {
        .HwPinId = 16,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-113, Feature-GPIO */
    {
        .HwPinId = 17,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-114, Feature-ADC1_SE16 */
    {
        .HwPinId = 18,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-115, Feature-ADC1_SE17 */
    {
        .HwPinId = 19,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-118, Feature-ADC1_SE18 */
    {
        .HwPinId = 22,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-119, Feature-ADC1_SE19 */
    {
        .HwPinId = 23,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-120, Feature-ADC1_SE20 */
    {
        .HwPinId = 24,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-123, Feature-ADC1_SE21 */
    {
        .HwPinId = 27,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-124, Feature-ADC1_SE22 */
    {
        .HwPinId = 28,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-125, Feature-ADC1_SE23 */
    {
        .HwPinId = 29,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ANA,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-126, Feature-GPIO */
    {
        .HwPinId = 30,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
};


PORT_CONST const static Port_PinConfigType Port_E_PinsConfig[] = {
    
    /* PCR-128, Feature-GPIO */
    {
        .HwPinId = 0,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-129, Feature-SPI0_SIN */
    {
        .HwPinId = 1,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT2,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-130, Feature-GPIO */
    {
        .HwPinId = 2,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-131, Feature-GPIO */
    {
        .HwPinId = 3,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-132, Feature-CAN0_RX */
    {
        .HwPinId = 4,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT5,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-133, Feature-CAN0_TX */
    {
        .HwPinId = 5,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_ALT5,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-134, Feature-GPIO */
    {
        .HwPinId = 6,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-135, Feature-GPIO */
    {
        .HwPinId = 7,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-136, Feature-GPIO */
    {
        .HwPinId = 8,
        .Direction = PORT_PIN_OUT,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_CHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_HIGH,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_CHANGEABLE,
        .PullConfig = PORT_PIN_PULLDOWN,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-137, Feature-GPIO */
    {
        .HwPinId = 9,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-138, Feature-GPIO */
    {
        .HwPinId = 10,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-139, Feature-GPIO */
    {
        .HwPinId = 11,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-140, Feature-GPIO */
    {
        .HwPinId = 12,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-141, Feature-GPIO */
    {
        .HwPinId = 13,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-142, Feature-GPIO */
    {
        .HwPinId = 14,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-143, Feature-GPIO */
    {
        .HwPinId = 15,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-144, Feature-GPIO */
    {
        .HwPinId = 16,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-147, Feature-GPIO */
    {
        .HwPinId = 19,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-148, Feature-GPIO */
    {
        .HwPinId = 20,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-149, Feature-GPIO */
    {
        .HwPinId = 21,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-150, Feature-GPIO */
    {
        .HwPinId = 22,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-151, Feature-GPIO */
    {
        .HwPinId = 23,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-152, Feature-GPIO */
    {
        .HwPinId = 24,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
    
    /* PCR-153, Feature-GPIO */
    {
        .HwPinId = 25,
        .Direction = PORT_PIN_IN,
        .IsDirectionChangeable= PORT_PIN_DIRECTION_UNCHANGEABLE,
        .InitLevel = PORT_PIN_LEVEL_LOW,
        .InitMode=  PORT_PIN_MODE_GPIO,
        .IsModeChangeable = PORT_PIN_MODE_NOT_CHANGEABLE,
        .PullConfig = PORT_PIN_NO_PULL,
        .DriveStrength = PORT_PIN_LOW_DRIVE_STRENGTH,
        .SlewRate = PORT_PIN_FAST_SLEW_RATE,
        .PassiveFilter = (boolean)0,
        .DigitalFilter = (boolean)0,
        .DigitalFilterWidth = 0,
        .InvertEnable = (boolean)0,
    },
};




PORT_CONST const Port_GroupConfigType PortGroup_Config[PORT_TOTAL_NUMBER] ={
     /* PORT_A Config */
    {
        .HwPortId = 0,
        .NumberOfPortPins = 23,
        .PortPinConfigPtr = &Port_A_PinsConfig[0],
    },
    /* PORT_B Config */
    {
        .HwPortId = 1,
        .NumberOfPortPins = 27,
        .PortPinConfigPtr = &Port_B_PinsConfig[0],
    },
    /* PORT_C Config */
    {
        .HwPortId = 2,
        .NumberOfPortPins = 24,
        .PortPinConfigPtr = &Port_C_PinsConfig[0],
    },
    /* PORT_D Config */
    {
        .HwPortId = 3,
        .NumberOfPortPins = 25,
        .PortPinConfigPtr = &Port_D_PinsConfig[0],
    },
    /* PORT_E Config */
    {
        .HwPortId = 4,
        .NumberOfPortPins = 24,
        .PortPinConfigPtr = &Port_E_PinsConfig[0],
    },
};

PORT_CONST const Port_ConfigType Port_Config = {
.NumberOfGroup = PORT_TOTAL_NUMBER,
    .PortGroupConfigPtr = &PortGroup_Config[0],
};


#define PORT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"

