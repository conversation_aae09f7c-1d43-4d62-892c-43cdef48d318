/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Wdg_Lld_PBcfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                          INCLUDE FILES                                          
==================================================================================================*/
#include "Wdg_Lld_PBcfg.h"

/*==================================================================================================
 *                                 SOURCE FILE VERSION INFORMATION                                 
==================================================================================================*/
#define WDG_LLD_PBCFG_VENDOR_ID_C           (180)
#define WDG_LLD_PBCFG_AR_REL_MAJOR_VER_C    (4)
#define WDG_LLD_PBCFG_AR_REL_MINOR_VER_C    (4)
#define WDG_LLD_PBCFG_AR_REL_REVISION_VER_C (0)
#define WDG_LLD_PBCFG_SW_MAJOR_VER_C        (2)
#define WDG_LLD_PBCFG_SW_MINOR_VER_C        (2)
#define WDG_LLD_PBCFG_SW_PATCH_VER_C        (0)

/*==================================================================================================
 *                                         GLOBAL CONSTANTS                                         
==================================================================================================*/
#define WDG_180_INST0_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst0_MemMap.h"

WDG_180_INST0_CONST const Wdg_Lld_ConfigType Wdg_Lld_OffModeSettings_Config = 
{
    .ResetInvalidApbEnable = TRUE,
    .WindowMode = FALSE,
    .InterruptBeforeReset = FALSE,
    .ClockSource = WDG_LLD_CLKSRC_SIRC,
    .DisableInDeepSleepMode = FALSE,
    .DisableInDebugMode = TRUE,
#if defined(CPU_YTM32B1MC0)
    .TimerOverflowValue = 0xC00UL,
#else
    .TimerOverflowValue = 0x300UL,
#endif
    .WindowValue = 0UL,
    .Lock = WDG_LLD_SOFTLOCK,
    .WdgCallback = NULL_PTR
};

WDG_180_INST0_CONST const Wdg_Lld_ConfigType Wdg_Lld_SlowModeSettings_Config = 
{
    .ResetInvalidApbEnable = TRUE,
    .WindowMode = TRUE,
    .InterruptBeforeReset = TRUE,
    .ClockSource = WDG_LLD_CLKSRC_SIRC,
    .DisableInDeepSleepMode = TRUE,
    .DisableInDebugMode = TRUE,
    .TimerOverflowValue = 12000000UL,
    .WindowValue = 6000000UL,
    .Lock = WDG_LLD_SOFTLOCK,
    .WdgCallback = Wdg_180_Inst0_Callback
};

WDG_180_INST0_CONST const Wdg_Lld_ConfigType Wdg_Lld_FastModeSettings_Config = 
{
    .ResetInvalidApbEnable = TRUE,
    .WindowMode = TRUE,
    .InterruptBeforeReset = TRUE,
    .ClockSource = WDG_LLD_CLKSRC_SXOSC,
    .DisableInDeepSleepMode = TRUE,
    .DisableInDebugMode = TRUE,
    .TimerOverflowValue = 16000UL,
    .WindowValue = 3200UL,
    .Lock = WDG_LLD_SOFTLOCK,
    .WdgCallback = Wdg_180_Inst0_Callback
};

#define WDG_180_INST0_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst0_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

