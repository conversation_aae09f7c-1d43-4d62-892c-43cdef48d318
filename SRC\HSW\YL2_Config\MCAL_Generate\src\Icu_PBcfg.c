/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Icu_PBcfg.c
 * @brief 
 * 
 */


#include "Icu.h"
#include "Icu_Lld_Etmr.h"
#include "Icu_Lld_Port.h"
#include "Icu_Base.h"
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ICU_VENDOR_ID_PBCFG_C (180)
#define ICU_AR_REL_MAJOR_VER_PBCFG_C (4)
#define ICU_AR_REL_MINOR_VER_PBCFG_C (4)
#define ICU_AR_REL_REVISION_VER_PBCFG_C (0)
#define ICU_SW_MAJOR_VER_PBCFG_C (2)
#define ICU_SW_MINOR_VER_PBCFG_C (2)
#define ICU_SW_PATCH_VER_PBCFG_C (0)

/*================================================================================================== */

 extern void IcuNotificationCh1_SW3_PTB3(void); 
 extern void IcuNotificationCh2_SW2_PTE9(void); 
 extern void IcuNotificationCh3_CAN_RX_PTD2(void); 
 extern void IcuSignalNotificationCh4_LIN_RX_PTB0(void); 





#define ICU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"

ICU_CONST const Icu_ChannelConfigType Icu_ChannelConfig[5] = 
{
    {
        .IcuChannelId = 0,
        .IcuHwIp = ICU_ETMR,
        .IcuHwIpInstance = ICU_ETMR5,
        .IcuHwChannel = 0,
        .IcuDefaultStartEdge = ICU_RISING_EDGE,
        .IcuWakeupCapability = FALSE,
#if (ICU_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .IcuWakeupSource = (EcuM_WakeupSourceType)0,
#endif
        .IcuMeasurementMode = ICU_MODE_SIGNAL_MEASUREMENT,        
        .SignalMeasurementProperty = ICU_DUTY_CYCLE,
    },
    {
        .IcuChannelId = 1,
        .IcuHwIp = ICU_PORT,
        .IcuHwIpInstance = ICU_PORTB,
        .IcuHwChannel = 3,
        .IcuDefaultStartEdge = ICU_RISING_EDGE,
        .IcuWakeupCapability = FALSE,
#if (ICU_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .IcuWakeupSource = (EcuM_WakeupSourceType)0,
#endif
        .IcuMeasurementMode = ICU_MODE_SIGNAL_EDGE_DETECT,        
        .SignalNotificationPtr = IcuNotificationCh1_SW3_PTB3,
    },
    {
        .IcuChannelId = 2,
        .IcuHwIp = ICU_PORT,
        .IcuHwIpInstance = ICU_PORTE,
        .IcuHwChannel = 9,
        .IcuDefaultStartEdge = ICU_RISING_EDGE,
        .IcuWakeupCapability = TRUE,
#if (ICU_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .IcuWakeupSource = EcuMConf_EcuMWakeupSource_EcuMWakeupSource_0,
#endif
        .IcuMeasurementMode = ICU_MODE_SIGNAL_EDGE_DETECT,        
        .SignalNotificationPtr = IcuNotificationCh2_SW2_PTE9,
    },
    {
        .IcuChannelId = 3,
        .IcuHwIp = ICU_PORT,
        .IcuHwIpInstance = ICU_PORTD,
        .IcuHwChannel = 2,
        .IcuDefaultStartEdge = ICU_RISING_EDGE,
        .IcuWakeupCapability = TRUE,
#if (ICU_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .IcuWakeupSource = EcuMConf_EcuMWakeupSource_EcuMWakeupSource_0,
#endif
        .IcuMeasurementMode = ICU_MODE_SIGNAL_EDGE_DETECT,        
        .SignalNotificationPtr = IcuNotificationCh3_CAN_RX_PTD2,
    },
    {
        .IcuChannelId = 4,
        .IcuHwIp = ICU_PORT,
        .IcuHwIpInstance = ICU_PORTB,
        .IcuHwChannel = 0,
        .IcuDefaultStartEdge = ICU_RISING_EDGE,
        .IcuWakeupCapability = TRUE,
#if (ICU_WAKEUP_FUNCTIONALITY_API == STD_ON)
        .IcuWakeupSource = EcuMConf_EcuMWakeupSource_EcuMWakeupSource_0,
#endif
        .IcuMeasurementMode = ICU_MODE_SIGNAL_EDGE_DETECT,        
        .SignalNotificationPtr = IcuSignalNotificationCh4_LIN_RX_PTB0,
    },
};

 
ICU_CONST const IcuEtmrChannelType EtmrListChannels_0[1]=
{
    {         
        .ChannelNum = 0,
        },
};


ICU_CONST const Icu_EtmrConfigType Icu_EtmrConfig[1]=
{
    {
        .IcuEtmrModule = ICU_ETMR5,
        .IcuEtmrCount = 1,
        .IcuEtmrChannelPtr = (IcuEtmrChannelType*)EtmrListChannels_0,
        .IcuEtmrClockSource = ICU_MC_CLOCK_SOURCE_FASTBUSCLK,
        .IcuEtmrPrescaler = 0,    
        .IcuEtmrModValue = 0,
    },    
};
/*
ICU_CONST const Icu_LptmrConfigType Icu_LptmrConfig[0]=
{

};
*/
 
ICU_CONST const IcuPortChannelType PortListChannels_0[2]=
{
    {         
        .PortChannel = 3,
        },
    {         
        .PortChannel = 0,
        },
};
ICU_CONST const IcuPortChannelType PortListChannels_1[1]=
{
    {         
        .PortChannel = 9,
        },
};
ICU_CONST const IcuPortChannelType PortListChannels_2[1]=
{
    {         
        .PortChannel = 2,
        },
};

ICU_CONST const Icu_PortConfigType Icu_PortConfig[3]=
{
    {
        .IcuPortModule = ICU_PORTB,
        .IcuPortCount = 4,
        .IcuPortChannelPtr = (IcuPortChannelType*)PortListChannels_0
    },
    {
        .IcuPortModule = ICU_PORTE,
        .IcuPortCount = 4,
        .IcuPortChannelPtr = (IcuPortChannelType*)PortListChannels_1
    },
    {
        .IcuPortModule = ICU_PORTD,
        .IcuPortCount = 4,
        .IcuPortChannelPtr = (IcuPortChannelType*)PortListChannels_2
    },

};





ICU_CONST const Icu_HwInterruptConfigType Icu_HwInterruptConfig[4]=
{
    {         
        .IcuIsrHwId = ICU_ETMR_5_CH_0,
        .IcuIsrEnable = TRUE,     
    },
    {         
        .IcuIsrHwId = ICU_PORT_1,
        .IcuIsrEnable = TRUE,     
    },
    {         
        .IcuIsrHwId = ICU_PORT_4,
        .IcuIsrEnable = TRUE,     
    },
    {         
        .IcuIsrHwId = ICU_PORT_3,
        .IcuIsrEnable = TRUE,     
    },
};
ICU_CONST const Icu_ConfigType Icu_Config = {
    .ChannelConfigPtr = (Icu_ChannelConfigType*)Icu_ChannelConfig,
    .IcuEtmrConfigPtr = (Icu_EtmrConfigType*) Icu_EtmrConfig,
    .IcuLptmrConfigPtr = (Icu_LptmrConfigType*)NULL_PTR,
    .IcuPortConfigPtr = (Icu_PortConfigType*) Icu_PortConfig,
    .IcuLpCmpConfigPtr = (Icu_LpCmpConfigType*) NULL_PTR,
    .IcuHwInterruptConfigPtr = (Icu_HwInterruptConfigType*)Icu_HwInterruptConfig,
#if WKU_SUPPORT == STD_ON
    .IcuWakeUpNotificationPtr = NULL_PTR,
#endif
    .IcuChannelCount = 5,
};
#define ICU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"

