/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Fee_Cfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fee.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FEE_VENDOR_ID_CFG_C             (180)
#define FEE_AR_REL_MAJOR_VER_CFG_C      (4)
#define FEE_AR_REL_MINOR_VER_CFG_C      (4)
#define FEE_AR_REL_REVISION_VER_CFG_C   (0)
#define FEE_SW_MAJOR_VER_CFG_C          (2)
#define FEE_SW_MINOR_VER_CFG_C          (2)
#define FEE_SW_PATCH_VER_CFG_C          (0)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and Fee header file are of the same vendor */
#if (FEE_VENDOR_ID_CFG_C != FEE_VENDOR_ID)
    #error "Fee_Cfg.c and Fee.h have different vendor ids"
#endif
/* Check if current file and Fee header file are of the same Autosar version */
#if ((FEE_AR_REL_MAJOR_VER_CFG_C    != FEE_AR_REL_MAJOR_VER) || \
     (FEE_AR_REL_MINOR_VER_CFG_C    != FEE_AR_REL_MINOR_VER) || \
     (FEE_AR_REL_REVISION_VER_CFG_C != FEE_AR_REL_REVISION_VER) \
    )
    #error "AutoSar Version Numbers of Fee_Cfg.c and Fee.h are different"
#endif
/* Check if current file and Fee header file are of the same Software version */
#if ((FEE_SW_MAJOR_VER_CFG_C != FEE_SW_MAJOR_VER) || \
     (FEE_SW_MINOR_VER_CFG_C != FEE_SW_MINOR_VER) || \
     (FEE_SW_PATCH_VER_CFG_C != FEE_SW_PATCH_VER) \
    )
    #error "Software Version Numbers of Fee_Cfg.c and Fee.h are different"
#endif

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/

#define FEE_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

/* Configuration of cluster group Fee_PowerOnDataGroup */
FEE_CONST static const Fee_ClusterType Fee_Fee_PowerOnDataGroup[3] = 
{
    /* FeeCluster_0 */
    {
    .StartAddr = 0x0U,
    .Length = 0x400U,
    },
    /* FeeCluster_1 */
    {
    .StartAddr = 0x400U,
    .Length = 0x400U,
    },
    /* FeeCluster_2 */
    {
    .StartAddr = 0x800U,
    .Length = 0x400U,
    },
};
/* Configuration of cluster group Fee_DTCDataGroup */
FEE_CONST static const Fee_ClusterType Fee_Fee_DTCDataGroup[9] = 
{
    /* FeeCluster_0 */
    {
    .StartAddr = 0xc00U,
    .Length = 0xc00U,
    },
    /* FeeCluster_1 */
    {
    .StartAddr = 0x1800U,
    .Length = 0xc00U,
    },
    /* FeeCluster_2 */
    {
    .StartAddr = 0x2400U,
    .Length = 0xc00U,
    },
    /* FeeCluster_3 */
    {
    .StartAddr = 0x3000U,
    .Length = 0xc00U,
    },
    /* FeeCluster_4 */
    {
    .StartAddr = 0x3c00U,
    .Length = 0xc00U,
    },
    /* FeeCluster_5 */
    {
    .StartAddr = 0x4800U,
    .Length = 0xc00U,
    },
    /* FeeCluster_6 */
    {
    .StartAddr = 0x5400U,
    .Length = 0xc00U,
    },
    /* FeeCluster_7 */
    {
    .StartAddr = 0x6000U,
    .Length = 0xc00U,
    },
    /* FeeCluster_8 */
    {
    .StartAddr = 0x6c00U,
    .Length = 0xc00U,
    },
};

/* Configuration of cluster group set */
FEE_CONST const Fee_ClusterGroupType Fee_ClrGrps[FEE_NUMBER_OF_CLUSTER_GROUPS] =
{
    /* Fee_PowerOnDataGroup */
    {
        .ClrPtr = Fee_Fee_PowerOnDataGroup,
        .ClrCount = 3U,
        .ReservedSize = 0U,
    },
    /* Fee_DTCDataGroup */
    {
        .ClrPtr = Fee_Fee_DTCDataGroup,
        .ClrCount = 9U,
        .ReservedSize = 0U,
    },
};

/* Configuration of Fee blocks */
FEE_CONST const Fee_BlockConfigType Fee_BlockConfig[FEE_CRT_CFG_NR_OF_BLOCKS] =
{
    /* The Fee Block 1 */
    {
        .BlockNumber = 1U,
        .BlockSize = 32U,
        .ClrGrp = 0U,
        .ImmediateData = 0U,
#if (FEE_SWAP_FOREIGN_BLOCKS_ENABLED == STD_ON) 
        .BlockAssignment = FEE_PROJECT_APPLICATION,
#else
        .BlockAssignment = FEE_PROJECT_RESERVED,
#endif
    },
    /* The Fee Block 2 */
    {
        .BlockNumber = 2U,
        .BlockSize = 64U,
        .ClrGrp = 1U,
        .ImmediateData = 0U,
#if (FEE_SWAP_FOREIGN_BLOCKS_ENABLED == STD_ON) 
        .BlockAssignment = FEE_PROJECT_APPLICATION,
#else
        .BlockAssignment = FEE_PROJECT_RESERVED,
#endif
    },
    /* The Fee Block 4 */
    {
        .BlockNumber = 4U,
        .BlockSize = 32U,
        .ClrGrp = 1U,
        .ImmediateData = 0U,
#if (FEE_SWAP_FOREIGN_BLOCKS_ENABLED == STD_ON) 
        .BlockAssignment = FEE_PROJECT_APPLICATION,
#else
        .BlockAssignment = FEE_PROJECT_RESERVED,
#endif
    },
};

#define FEE_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

