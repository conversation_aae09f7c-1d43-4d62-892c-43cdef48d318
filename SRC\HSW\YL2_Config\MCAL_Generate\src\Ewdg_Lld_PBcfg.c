/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Ewdg_Lld_PBcfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                          INCLUDE FILES                                          
==================================================================================================*/
#include "Ewdg_Lld_PBcfg.h"

/*==================================================================================================
 *                                 SOURCE FILE VERSION INFORMATION                                 
==================================================================================================*/
#define EWDG_LLD_PBCFG_VENDOR_ID_C              (180)
#define EWDG_LLD_PBCFG_AR_REL_MAJOR_VER_C       (4)
#define EWDG_LLD_PBCFG_AR_REL_MINOR_VER_C       (4)
#define EWDG_LLD_PBCFG_AR_REL_REVISION_VER_C    (0)
#define EWDG_LLD_PBCFG_SW_MAJOR_VER_C           (2)
#define EWDG_LLD_PBCFG_SW_MINOR_VER_C           (2)
#define EWDG_LLD_PBCFG_SW_PATCH_VER_C           (0)

/*==================================================================================================
 *                                         GLOBAL CONSTANTS                                         
==================================================================================================*/
#define WDG_180_INST1_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

WDG_180_INST1_CONST const Ewdg_Lld_ConfigType Ewdg_Lld_SlowModeSettings_Config = 
{
    .InterruptEnable = TRUE,
    .AssertLogic = EWDG_LLD_IN_ASSERT_DISABLED,
    .ClockSelect = EWDG_LLD_CLKSRC_SXOSC,
    .Prescaler = 150U,
    .CompareLow = 85U,
    .CompareHigh = 212U,
    .EwdgCallback = Wdg_180_Inst1_Callback
};

#define WDG_180_INST1_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

