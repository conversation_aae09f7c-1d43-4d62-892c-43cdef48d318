/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Wdg_180_Inst1_PBcfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                          INCLUDE FILES                                          
==================================================================================================*/
#include "Wdg_180_Inst1_PBcfg.h"
#include "Ewdg_Lld_PBcfg.h"

/*==================================================================================================
 *                                 SOURCE FILE VERSION INFORMATION                                 
==================================================================================================*/
#define WDG_180_INST1_VENDOR_ID_PBCFG_C             (180)
#define WDG_180_INST1_AR_REL_MAJOR_VER_PBCFG_C      (4)
#define WDG_180_INST1_AR_REL_MINOR_VER_PBCFG_C      (4)
#define WDG_180_INST1_AR_REL_REVISION_VER_PBCFG_C   (0)
#define WDG_180_INST1_SW_MAJOR_VER_PBCFG_C          (2)
#define WDG_180_INST1_SW_MINOR_VER_PBCFG_C          (2)
#define WDG_180_INST1_SW_PATCH_VER_PBCFG_C          (0)

/*==================================================================================================
 *                                         LOCAL CONSTANTS                                         
==================================================================================================*/
#define WDG_180_INST1_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

WDG_180_INST1_CONST static const Wdg_180_Inst1_ModeType Wdg_180_Inst1_SlowModeSettings_Config = 
{
        
    /* 0 --- CompareLow --- TriggerPeriod --- CompareHigh
     * TriggerPeriod = CompareHigh - (CompareHigh - CompareLow) / 2
     */
    .TriggerPeriod = 28000000UL,
    .EwdgConfig = &Ewdg_Lld_SlowModeSettings_Config
};

/*==================================================================================================
 *                                         GLOBAL CONSTANTS                                         
==================================================================================================*/
WDG_180_INST1_CONST const Wdg_180_Inst1_ConfigType Wdg_180_Inst1_Config = 
{
    .DefaultMode = WDGIF_SLOW_MODE,
#if (WDG_180_INST1_MANUAL_SERVICE == STD_OFF)
    .TimerChannel = GptConf_GptChannelConfiguration_Wdg_180_Inst1,
    .TriggerSourceClock = 40000UL,
#endif
    .ModeSettings = 
    {
        [WDGIF_OFF_MODE] = NULL_PTR,
        [WDGIF_SLOW_MODE] = &Wdg_180_Inst1_SlowModeSettings_Config,
        [WDGIF_FAST_MODE] = NULL_PTR
    }
};

#define WDG_180_INST1_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

