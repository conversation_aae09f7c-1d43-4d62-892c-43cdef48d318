/**
*   @file    CddUart_Cfg.c
*   @version 
*   @brief   Mcu Uart configure file.
*/
/*==================================================================================================
*   Project              : YTMicro AUTOSAR 4.4.0 MCAL
*   Platform             : ARM
*   Peripheral           : uart
*   Dependencies         : none
*
*   Autosar Version      : V4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : V2.2.0
*   
*
*   (c) Copyright 2020-2023 Yuntu Microelectronics co.,ltd. 
*   All Rights Reserved.
==================================================================================================*/
#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "CddUart.h"
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CDDUART_VENDOR_ID_CFG_C               (180)
#define CDDUART_AR_REL_MAJOR_VER_CFG_C        (4)
#define CDDUART_AR_REL_MINOR_VER_CFG_C        (4)
#define CDDUART_AR_REL_REVISION_VER_CFG_C     (0)
#define CDDUART_SW_MAJOR_VER_CFG_C            (2)
#define CDDUART_SW_MINOR_VER_CFG_C            (2)
#define CDDUART_SW_PATCH_VER_CFG_C            (0)
/*==================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/
#define UART_CORE_ID     ((uint32)0U)
/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/


/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

