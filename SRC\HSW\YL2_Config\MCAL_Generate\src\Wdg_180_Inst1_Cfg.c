/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Wdg_180_Inst1_Cfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                          INCLUDE FILES
==================================================================================================*/
#include "Wdg_180_Inst1_Cfg.h"

/*==================================================================================================
 *                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define WDG_180_INST1_VENDOR_ID_CFG_C             (180)
#define WDG_180_INST1_AR_REL_MAJOR_VER_CFG_C      (4)
#define WDG_180_INST1_AR_REL_MINOR_VER_CFG_C      (4)
#define WDG_180_INST1_AR_REL_REVISION_VER_CFG_C   (0)
#define WDG_180_INST1_SW_MAJOR_VER_CFG_C          (2)
#define WDG_180_INST1_SW_MINOR_VER_CFG_C          (2)
#define WDG_180_INST1_SW_PATCH_VER_CFG_C          (0)

/*==================================================================================================
 *                                         GLOBAL CONSTANTS
==================================================================================================*/
#define WDG_180_INST1_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

WDG_180_INST1_CONST const uint16 Wdg_180_Inst1_InitialTimeoutCfg = WDG_180_INST1_INITIAL_TIMEOUT;

WDG_180_INST1_CONST const uint16 Wdg_180_Inst1_MaxTimeoutCfg = WDG_180_INST1_MAX_TIMEOUT;

#define WDG_180_INST1_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

#define WDG_180_INST1_START_SEC_CONST_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"


#define WDG_180_INST1_STOP_SEC_CONST_UNSPECIFIED
#include "Wdg_180_Inst1_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

