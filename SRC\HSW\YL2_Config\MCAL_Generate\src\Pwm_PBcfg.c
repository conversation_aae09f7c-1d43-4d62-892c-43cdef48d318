/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                        INCLUDE FILES
==================================================================================================*/
#include "Pwm_PBcfg.h"
#include "Pwm_Lld_Etmr_PBcfg.h"

/*==================================================================================================
 *                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_VENDOR_ID_PBCFG_C           (180)
#define PWM_AR_REL_MAJOR_VER_PBCFG_C    (4)
#define PWM_AR_REL_MINOR_VER_PBCFG_C    (4)
#define PWM_AR_REL_REVISION_VER_PBCFG_C (0)
#define PWM_SW_MAJOR_VER_PBCFG_C        (2)
#define PWM_SW_MINOR_VER_PBCFG_C        (2)
#define PWM_SW_PATCH_VER_PBCFG_C        (0)

/*==================================================================================================
 *                                        GLOBAL CONSTANTS                                        
==================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/**
 * @brief   PWM channels configuration array
 */
PWM_CONST static const Pwm_ChannelConfigType Pwm_ChannelConfigs[PWM_PBCFG_CHANNELS_COUNT] = 
{
/* GREEN_LED */
    {
        .ChannelId = 0U,
        .ChannelClass = PWM_VARIABLE_PERIOD,
        .MldChCfg = 
        {
            .ChInstId = PWM_LLD_ETMR_INST_ID_ETMR_3_CH_0,
            .ChType = PWM_MLD_CHN_ETMR,
            .ChDutyCycle = 0U,
            .EtmrChCfg = &Pwm_Lld_Etmr_ChCfg_Inst3_Ch0
        },
        .ChannelIdleState = PWM_LOW,
#if (PWM_NOTIFICATION_SUPPORTED == STD_ON)
        .ChannelNotification = NULL_PTR
#endif
    },
/* RED_LED */
    {
        .ChannelId = 1U,
        .ChannelClass = PWM_VARIABLE_PERIOD,
        .MldChCfg = 
        {
            .ChInstId = PWM_LLD_ETMR_INST_ID_ETMR_3_CH_1,
            .ChType = PWM_MLD_CHN_ETMR,
            .ChDutyCycle = 0U,
            .EtmrChCfg = &Pwm_Lld_Etmr_ChCfg_Inst3_Ch1
        },
        .ChannelIdleState = PWM_LOW,
#if (PWM_NOTIFICATION_SUPPORTED == STD_ON)
        .ChannelNotification = NULL_PTR
#endif
    },
/* BLUE_LED */
    {
        .ChannelId = 2U,
        .ChannelClass = PWM_VARIABLE_PERIOD,
        .MldChCfg = 
        {
            .ChInstId = PWM_LLD_ETMR_INST_ID_ETMR_3_CH_2,
            .ChType = PWM_MLD_CHN_ETMR,
            .ChDutyCycle = 0U,
            .EtmrChCfg = &Pwm_Lld_Etmr_ChCfg_Inst3_Ch2
        },
        .ChannelIdleState = PWM_LOW,
#if (PWM_NOTIFICATION_SUPPORTED == STD_ON)
        .ChannelNotification = NULL_PTR
#endif
    },
};

/**
 * @brief   PWM instances configuration array
 */
PWM_CONST static const Pwm_Mld_InstCfgType Pwm_InstanceConfigs[PWM_PBCFG_INSTANCES_COUNT] =
{
/* PwmEtmr_0 */
    {
        .InstId = 3U,
        .InstType = PWM_MLD_INST_ETMR,
        .EtmrInstCfg = &Pwm_Lld_Etmr_InstCfg_Inst3
    },
};

/**
 * @brief   PWM configuration
 */
PWM_CONST const Pwm_ConfigType Pwm_Config = 
{
    .ChannelCount = PWM_PBCFG_CHANNELS_COUNT,
    .PwmChannelsConfig = &Pwm_ChannelConfigs,
    .InstanceCount = PWM_PBCFG_INSTANCES_COUNT,   
    .PwmInstancesConfig = &Pwm_InstanceConfigs
};

#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

