/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */



#include "Ocu.h"
#include "Ocu_Cfg.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define OCU_VENDOR_ID_PBCFG_C               (180)
#define OCU_AR_REL_MAJOR_VER_PBCFG_C        (4)
#define OCU_AR_REL_MINOR_VER_PBCFG_C        (4)
#define OCU_AR_REL_REVISION_VER_PBCFG_C     (0)
#define OCU_SW_MAJOR_VER_PBCFG_C            (2)
#define OCU_SW_MINOR_VER_PBCFG_C           (2)
#define OCU_SW_PATCH_VER_PBCFG_C           (0)

/*================================================================================================== */



 /*================================== Configuration for Ftm =======================================*/

    /*==================================================================================================
    *                                       GLOBAL VARIABLES
    ==================================================================================================*/

    /*==================================================================================================
    *                                    LOCAL FUNCTION PROTOTYPES
    ==================================================================================================*/

    /*==================================================================================================
    *                                        LOCAL FUNCTIONS
    ==================================================================================================*/

    /*==================================================================================================
    *                                        GLOBAL FUNCTIONS
    ==================================================================================================*/


#define OCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Ocu_MemMap.h"
/** @brief Array of configured Ocu channels */
OCU_CONST static const Ocu_ChannelHwResourceType OcuHwResourceConfig[OCU_CONF_CHANNELS_PB] =
{
  {
	.HwResourceId = ETMR_0_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_0_CH_7,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_1_CH_7,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_2_CH_7,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_3_CH_7,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_4_CH_7,
	.ChannelEnable = (boolean)TRUE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_0,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_1,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_2,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_3,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_4,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_5,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_6,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
  {
	.HwResourceId = ETMR_5_CH_7,
	.ChannelEnable = (boolean)FALSE,
	.OcuIsrEnable = (boolean)FALSE,
  },
};
OCU_CONST static const Ocu_ChannelConfigType OcuChannelConfig[1] =
{
  {
	.ChannelId = 0,
	.AssignedHardwareChannel = 0,
	.HwChannel = ETMR_4_CH_7,
	.DefaultThreshold = 1000,
	.MaxCounterValue = 6000,
	.OcuOutputPinUsed = (boolean)TRUE,
	/** @brief Ocu notification function */
	.PfChNotification = NULL,
	.OcuOutputPinDefaultState = OCU_LOW,
	.OcuOutputPinAction = OCU_TOGGLE,
  },
};

OCU_CONST static const Ocu_ChannelOcuHWSpecificType OcuHWSpecificConfig[1] =
{
  {
	.EtmrModule = OCU_ETMR_4,
	.ClockSource = ETMR_CLOCK_SOURCE_INTERNALCLK,
	.Prescale = 0,
	
	.DebugMode = STD_ON,
  },
};

OCU_CONST const Ocu_ConfigType Ocu_Config = {
	/** @brief Number of configured Ocu channels */
	.NumChannels = 1,
	.InstanceCount = 1,
	/** @brief Pointer to array of Ocu channels */
	.OcuHwResource = OcuHwResourceConfig,
	.OcuChannel = OcuChannelConfig,
	.OcuHWSpecific = OcuHWSpecificConfig,
};
#define OCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Ocu_MemMap.h"

