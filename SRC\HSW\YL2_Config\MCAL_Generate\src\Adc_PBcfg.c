/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */

#include "Adc.h"
#include "Adc_Cfg.h"
#ifdef ADC_DMA_SUPPORTED
#include "CddDma_Cfg.h"
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ADC_VENDOR_ID_PBCFG_C               (180)
#define ADC_AR_REL_MAJOR_VER_PBCFG_C        (4)
#define ADC_AR_REL_MINOR_VER_PBCFG_C        (4)
#define ADC_AR_REL_REVISION_VER_PBCFG_C     (0)
#define ADC_SW_MAJOR_VER_PBCFG_C            (2)
#define ADC_SW_MINOR_VER_PBCFG_C           (2)
#define ADC_SW_PATCH_VER_PBCFG_C           (0)

/*================================================================================================== */
/**
 * @brief          Config number of ADC Hw units.
 */
#define ADC_UNIT_NUMBER                         (2U)

/**
 * @brief          Max number of ADC Hw units.
 */
#define ADC_GROUP_NUMBER                        (4U)

/*****************************************************************************************************/
#if(ADC_GRP_NOTIF_CAPABILITY == STD_ON)

extern void Adc0_Group1Notification(void); 
extern void Adc0_Group2Notification(void); 
extern void Adc1_Group3Notification(void); 
#endif

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/*Group channels definetion*/
ADC_CONST static const Adc_ChannelType ADC0Group_ExinChns_Channel[7] = 
{
    ADC0_SE0_ADCH0,
    ADC0_SE1_ADCH1,
    ADC0_SE16_ADCH16,
    ADC0_SE17_ADCH17,
    ADC0_SE18_ADCH18,
    ADC0_SE19_ADCH19,
    ADC0_SE20_ADCH20,
};
ADC_CONST static const Adc_ChannelType ADC0Group_MultChns_Channel[6] = 
{
    ADC0_SE0_ADCH0,
    ADC0_SE1_ADCH1,
    ADC0_VDD25_INT0_ADCH24,
    ADC0_VDD_INT0_ADCH26,
    ADC0_VSS_INT0_ADCH27,
    ADC0_VREFH_INT0_ADCH28,
};
ADC_CONST static const Adc_ChannelType Adc0Group_ContGroup_Channel[3] = 
{
    ADC0_SE0_ADCH0,
    ADC0_SE1_ADCH1,
    ADC0_VDD25_INT0_ADCH24,
};
ADC_CONST static const Adc_ChannelType ADC1Group_ExChns_Channel[7] = 
{
    ADC1_SE16_ADCH16,
    ADC1_SE17_ADCH17,
    ADC1_SE18_ADCH18,
    ADC1_SE19_ADCH19,
    ADC1_SE20_ADCH20,
    ADC1_SE21_ADCH21,
    ADC1_SE22_ADCH22,
};

/**/

ADC_CONST static const Adc_GroupDefType ADC0Group_ExinChns_ChannelsInf = 
{
    .GroupChannels       = &ADC0Group_ExinChns_Channel[0],
    .GroupChannelsNum    = 7,
    .GroupInHwUnitId     = ADC_0,
};
ADC_CONST static const Adc_GroupDefType ADC0Group_MultChns_ChannelsInf = 
{
    .GroupChannels       = &ADC0Group_MultChns_Channel[0],
    .GroupChannelsNum    = 6,
    .GroupInHwUnitId     = ADC_0,
};
ADC_CONST static const Adc_GroupDefType Adc0Group_ContGroup_ChannelsInf = 
{
    .GroupChannels       = &Adc0Group_ContGroup_Channel[0],
    .GroupChannelsNum    = 3,
    .GroupInHwUnitId     = ADC_0,
};

ADC_CONST static const Adc_GroupDefType ADC1Group_ExChns_ChannelsInf = 
{
    .GroupChannels       = &ADC1Group_ExChns_Channel[0],
    .GroupChannelsNum    = 7,
    .GroupInHwUnitId     = ADC_1,
};


#if (ADC_UNIT_NUMBER != 0)
#if (ADC_UNIT_NUMBER > ADC_MAX_HW_UNITS)
    #error "Config too many ADC Hw Unit !"
#else
ADC_CONST const Adc_HwUnitType Adc_HwUnitConfiguration[ADC_UNIT_NUMBER] = {
{ 
    .HwUnitId = ADC_0,
    .ClockSource = ADC_BUSCLK,
    .Prescale = 0,
        .StartupTime = 41,
    .InternalTempSensor = FALSE,
},
{ 
    .HwUnitId = ADC_1,
    .ClockSource = ADC_BUSCLK,
    .Prescale = 0,
        .StartupTime = 25,
    .InternalTempSensor = FALSE,
#ifdef ADC_DMA_SUPPORTED
    .DmaChannel = CddDmaConf_CddDmaConfig_ADC1_DMA_CHANNEL_2,
#endif
},
};
#endif /* ADC_UNIT_NUMBER > ADC_MAX_HW_UNITS */
#endif /* ADC_UNIT_NUMBER != 0 */

ADC_CONST const Adc_ChannelSampleType ADC0Group_ExinChns_ChannelSample = {
    .ChannelSampTime = 5,
    .ChannelConvTime = 0,
    .ChannelRefVoltsrcHigh = 0,
    .ChannelRefVoltsrcLow = 0,
    .ChannelResolution = ADC_RESOLUTION_12BIT,
};
ADC_CONST const Adc_ChannelSampleType ADC0Group_MultChns_ChannelSample = {
    .ChannelSampTime = 5,
    .ChannelConvTime = 0,
    .ChannelRefVoltsrcHigh = 0,
    .ChannelRefVoltsrcLow = 0,
    .ChannelResolution = ADC_RESOLUTION_12BIT,
};
ADC_CONST const Adc_ChannelSampleType Adc0Group_ContGroup_ChannelSample = {
    .ChannelSampTime = 5,
    .ChannelConvTime = 0,
    .ChannelRefVoltsrcHigh = 0,
    .ChannelRefVoltsrcLow = 0,
    .ChannelResolution = ADC_RESOLUTION_12BIT,
};
ADC_CONST const Adc_ChannelSampleType ADC1Group_ExChns_ChannelSample = {
    .ChannelSampTime = 5,
    .ChannelConvTime = 0,
    .ChannelRefVoltsrcHigh = 0,
    .ChannelRefVoltsrcLow = 0,
    .ChannelResolution = ADC_RESOLUTION_10BIT,
};

ADC_CONST const Adc_GroupSampleType ADC0Group_ExinChns_Configuration = 
{
    .GroupAccessMode = ADC_ACCESS_MODE_SINGLE,
    .GroupConversionMode = ADC_CONV_MODE_ONESHOT,
#if (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE)
    .GroupPriority = 0,
#endif /* (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE) */
    .GroupReplacement = ADC_GROUP_REPL_ABORT_RESTART,
    .GroupTriggSrc = ADC_TRIGG_SRC_SW,
#if (ADC_HW_TRIGGER_API == STD_ON)
    .HwTrigSrc = CDDTRG_PTU_OUTPUT,
#endif /* (ADC_HW_TRIGGER_API == STD_ON) */
    .StreamingBufferMode = ADC_STREAM_BUFFER_LINEAR,
    .StreamingNumSamples = 1,
    .GroupDefinition = &ADC0Group_ExinChns_ChannelsInf,
    .NotificationFunc = NULL_PTR,
    .IntcAndDmaCfg = ADC_INTCANDDMA_DISABLED,
};
ADC_CONST const Adc_GroupSampleType ADC0Group_MultChns_Configuration = 
{
    .GroupAccessMode = ADC_ACCESS_MODE_SINGLE,
    .GroupConversionMode = ADC_CONV_MODE_ONESHOT,
#if (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE)
    .GroupPriority = 0,
#endif /* (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE) */
    .GroupReplacement = ADC_GROUP_REPL_ABORT_RESTART,
    .GroupTriggSrc = ADC_TRIGG_SRC_SW,
#if (ADC_HW_TRIGGER_API == STD_ON)
    .HwTrigSrc = CDDTRG_PTU_OUTPUT,
#endif /* (ADC_HW_TRIGGER_API == STD_ON) */
    .StreamingBufferMode = ADC_STREAM_BUFFER_LINEAR,
    .StreamingNumSamples = 1,
    .GroupDefinition = &ADC0Group_MultChns_ChannelsInf,
    .NotificationFunc = Adc0_Group1Notification,
    .IntcAndDmaCfg = ADC_INTC_ENABLED,
};
ADC_CONST const Adc_GroupSampleType Adc0Group_ContGroup_Configuration = 
{
    .GroupAccessMode = ADC_ACCESS_MODE_SINGLE,
    .GroupConversionMode = ADC_CONV_MODE_CONTINUOUS,
#if (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE)
    .GroupPriority = 0,
#endif /* (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE) */
    .GroupReplacement = ADC_GROUP_REPL_ABORT_RESTART,
    .GroupTriggSrc = ADC_TRIGG_SRC_SW,
#if (ADC_HW_TRIGGER_API == STD_ON)
    .HwTrigSrc = CDDTRG_PTU_OUTPUT,
#endif /* (ADC_HW_TRIGGER_API == STD_ON) */
    .StreamingBufferMode = ADC_STREAM_BUFFER_LINEAR,
    .StreamingNumSamples = 1,
    .GroupDefinition = &Adc0Group_ContGroup_ChannelsInf,
    .NotificationFunc = Adc0_Group2Notification,
    .IntcAndDmaCfg = ADC_INTC_ENABLED,
};
ADC_CONST const Adc_GroupSampleType ADC1Group_ExChns_Configuration = 
{
    .GroupAccessMode = ADC_ACCESS_MODE_SINGLE,
    .GroupConversionMode = ADC_CONV_MODE_ONESHOT,
#if (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE)
    .GroupPriority = 0,
#endif /* (ADC_PRIORITY_IMPLEMENTATION != ADC_PRIORITY_NONE) */
    .GroupReplacement = ADC_GROUP_REPL_ABORT_RESTART,
    .GroupTriggSrc = ADC_TRIGG_SRC_SW,
#if (ADC_HW_TRIGGER_API == STD_ON)
    .HwTrigSrc = CDDTRG_PTU_OUTPUT,
#endif /* (ADC_HW_TRIGGER_API == STD_ON) */
    .StreamingBufferMode = ADC_STREAM_BUFFER_LINEAR,
    .StreamingNumSamples = 1,
    .GroupDefinition = &ADC1Group_ExChns_ChannelsInf,
    .NotificationFunc = Adc1_Group3Notification,
    .IntcAndDmaCfg = ADC_DMA_ENABLED,
};

ADC_CONST const Adc_GroupConfigType Adc_GourpsConfiguration[4] = 
{
    {
        .GroupId = AdcConf_AdcConfigSet_ADC0Group_ExinChns,
        .GroupSample = &ADC0Group_ExinChns_Configuration,
        .ChannelSample = &ADC0Group_ExinChns_ChannelSample,
    },
    {
        .GroupId = AdcConf_AdcConfigSet_ADC0Group_MultChns,
        .GroupSample = &ADC0Group_MultChns_Configuration,
        .ChannelSample = &ADC0Group_MultChns_ChannelSample,
    },
    {
        .GroupId = AdcConf_AdcConfigSet_Adc0Group_ContGroup,
        .GroupSample = &Adc0Group_ContGroup_Configuration,
        .ChannelSample = &Adc0Group_ContGroup_ChannelSample,
    },
    {
        .GroupId = AdcConf_AdcConfigSet_ADC1Group_ExChns,
        .GroupSample = &ADC1Group_ExChns_Configuration,
        .ChannelSample = &ADC1Group_ExChns_ChannelSample,
    },
};
ADC_CONST const Adc_ConfigType Adc_Config =
{
    .GroupCfgNum    = ADC_GROUP_NUMBER,
    .HwUnitCfgNum   = ADC_UNIT_NUMBER,
    .HwUnitUserCfg = &Adc_HwUnitConfiguration[0],
    .GroupUserCfg  = &Adc_GourpsConfiguration[0],
};

#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

