/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */

#include "Fls_Types.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_VENDOR_ID_PBCFG_C               (180)
#define FLS_AR_REL_MAJOR_VER_PBCFG_C        (4)
#define FLS_AR_REL_MINOR_VER_PBCFG_C        (4)
#define FLS_AR_REL_REVISION_VER_PBCFG_C     (0)
#define FLS_SW_MAJOR_VER_PBCFG_C            (2)
#define FLS_SW_MINOR_VER_PBCFG_C            (2)
#define FLS_SW_PATCH_VER_PBCFG_C            (0)

/*==================================================================================================
 *                                       Function Prototypes
==================================================================================================*/
extern void Fee_JobEndNotification(void); 
extern void Fee_JobErrorNotification(void); 

#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"
/*================================================================================================== */
FLS_CONST const Fls_SectorType Fls_SectorConfig[31] ={
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector0,
        .SectorStartAddress = 0x0U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x100000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector1,
        .SectorStartAddress = 0x400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x100400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector2,
        .SectorStartAddress = 0x800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x100800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector3,
        .SectorStartAddress = 0xc00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x100c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector4,
        .SectorStartAddress = 0x1000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x101000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector5,
        .SectorStartAddress = 0x1400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x101400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector6,
        .SectorStartAddress = 0x1800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x101800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector7,
        .SectorStartAddress = 0x1c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x101c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector8,
        .SectorStartAddress = 0x2000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x102000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector9,
        .SectorStartAddress = 0x2400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x102400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector10,
        .SectorStartAddress = 0x2800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x102800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector11,
        .SectorStartAddress = 0x2c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x102c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector12,
        .SectorStartAddress = 0x3000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x103000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector13,
        .SectorStartAddress = 0x3400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x103400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector14,
        .SectorStartAddress = 0x3800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x103800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector15,
        .SectorStartAddress = 0x3c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x103c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector16,
        .SectorStartAddress = 0x4000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x104000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector17,
        .SectorStartAddress = 0x4400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x104400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector18,
        .SectorStartAddress = 0x4800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x104800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector19,
        .SectorStartAddress = 0x4c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x104c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector20,
        .SectorStartAddress = 0x5000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x105000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector21,
        .SectorStartAddress = 0x5400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x105400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector22,
        .SectorStartAddress = 0x5800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x105800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector23,
        .SectorStartAddress = 0x5c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x105c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector24,
        .SectorStartAddress = 0x6000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x106000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector25,
        .SectorStartAddress = 0x6400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x106400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector26,
        .SectorStartAddress = 0x6800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x106800U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector27,
        .SectorStartAddress = 0x6c00U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x106c00U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector28,
        .SectorStartAddress = 0x7000U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x107000U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_Fls_DflashSector29,
        .SectorStartAddress = 0x7400U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x107400U,
        .AsyncAccess = TRUE,
    },
    {
                        
        .SectorId = FlsConf_FlsConfigSet_FlsSector_NVR_Key,
        .SectorStartAddress = 0x7800U,
        .SectorSize = 0x400U,
        .PageSize = 0x8U,
        .SectorHwStartAddress = 0x10000000U,
        .AsyncAccess = FALSE,
    },
};


FLS_CONST const Fls_ConfigType Fls_Config = {
    .AcEraseFunPtr = NULL_PTR,
    .AcWriteFunPtr = NULL_PTR,
    .JobEndNotificationFunPtr = Fee_JobEndNotification,
    .JobErrorNotificationFunPtr = Fee_JobErrorNotification,
    .DefaultMode = MEMIF_MODE_SLOW,
    .MaxReadFastMode = 10240U,
    .MaxReadNormalMode = 1024U,
    .MaxWriteFastMode = 256U,
    .MaxWriteNormalMode = 8U,
    .ConfiguredSectorNumber = 31U,
    .SectorList = Fls_SectorConfig,
};

#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

