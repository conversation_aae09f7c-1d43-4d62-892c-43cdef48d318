/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Pwm_Lld_Etmr_PBcfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
 *                                        INCLUDE FILES
==================================================================================================*/
#include "Pwm_Lld_Etmr_PBcfg.h"

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_LLD_ETMR_VENDOR_ID_PBCFG_C              (180)
#define PWM_LLD_ETMR_AR_REL_MAJOR_VER_PBCFG_C       (4)
#define PWM_LLD_ETMR_AR_REL_MINOR_VER_PBCFG_C       (4)
#define PWM_LLD_ETMR_AR_REL_REVISION_VER_PBCFG_C    (0)
#define PWM_LLD_ETMR_SW_MAJOR_VER_PBCFG_C           (2)
#define PWM_LLD_ETMR_SW_MINOR_VER_PBCFG_C           (2)
#define PWM_LLD_ETMR_SW_PATCH_VER_PBCFG_C           (0)

/*==================================================================================================
 *                                         GLOBAL CONSTANTS                                         
==================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"
                        
/*================================================================================================*/
/* Instance configuration structure PwmEtmr_0 */
PWM_CONST const Pwm_Lld_Etmr_InstCfgType Pwm_Lld_Etmr_InstCfg_Inst3 = 
{
    .DebugModeEnable = TRUE,
    .OvfIrqEn = FALSE,
    .ModTrgEn = FALSE,
    .MidTrgEn = FALSE,
    .InitTrgEn = FALSE,
    .Period = 120000U,
    .ClockPrescaler = 0U,
    .OutTrgWidth = 0,
    .OutTrgFreq = 0U,
    .PulseSrc = PWM_LLD_ETMR_OUTPULSESRC_CH0,
    .ClockSource = PWM_LLD_ETMR_CLKSRC_BUS,
    .ExClockSource = PWM_LLD_ETMR_EXCLKSRC_TCLK_IN0,
    .OutTrgSrc = PWM_LLD_ETMR_OUTTRGSRC_MATCH,
    .OvfCallback = NULL_PTR
};

/* Channel configuration PwmEtmrCh_0 */
PWM_CONST const Pwm_Lld_Etmr_ChCfgType Pwm_Lld_Etmr_ChCfg_Inst3_Ch0 = 
{
    .Val0TrgEn = FALSE,
    .Val1TrgEn = FALSE,
    .ChannelId = 0U,
    .DutyCycle = 0U,
    .DeadTime = 0U,
    .AlignMode = PWM_LLD_ETMR_ALIGN_MODE_CENTER,
    .Polarity = PWM_LLD_ETMR_POLARITY_NOT_INVERT,
    .IdleState = PWM_LLD_ETMR_OUTPUT_STATE_LOW,
    .ChMode = PWM_LLD_ETMR_MODE_INDEPENDENT
};
/* Channel configuration PwmEtmrCh_1 */
PWM_CONST const Pwm_Lld_Etmr_ChCfgType Pwm_Lld_Etmr_ChCfg_Inst3_Ch1 = 
{
    .Val0TrgEn = FALSE,
    .Val1TrgEn = FALSE,
    .ChannelId = 1U,
    .DutyCycle = 0U,
    .DeadTime = 0U,
    .AlignMode = PWM_LLD_ETMR_ALIGN_MODE_CENTER,
    .Polarity = PWM_LLD_ETMR_POLARITY_NOT_INVERT,
    .IdleState = PWM_LLD_ETMR_OUTPUT_STATE_LOW,
    .ChMode = PWM_LLD_ETMR_MODE_INDEPENDENT
};
/* Channel configuration PwmEtmrCh_2 */
PWM_CONST const Pwm_Lld_Etmr_ChCfgType Pwm_Lld_Etmr_ChCfg_Inst3_Ch2 = 
{
    .Val0TrgEn = FALSE,
    .Val1TrgEn = FALSE,
    .ChannelId = 2U,
    .DutyCycle = 0U,
    .DeadTime = 0U,
    .AlignMode = PWM_LLD_ETMR_ALIGN_MODE_CENTER,
    .Polarity = PWM_LLD_ETMR_POLARITY_NOT_INVERT,
    .IdleState = PWM_LLD_ETMR_OUTPUT_STATE_LOW,
    .ChMode = PWM_LLD_ETMR_MODE_INDEPENDENT
};

PWM_CONST static const Pwm_Lld_Etmr_ChCfgType * const Pwm_Lld_Etmr_ChCfgArr_Inst3[3U] = 
{
    &Pwm_Lld_Etmr_ChCfg_Inst3_Ch0,
    &Pwm_Lld_Etmr_ChCfg_Inst3_Ch1,
    &Pwm_Lld_Etmr_ChCfg_Inst3_Ch2,
};

/* User configuration structure PwmEtmr_0 */
PWM_CONST const Pwm_Lld_Etmr_CfgType Pwm_Lld_Etmr_Cfg_Inst3 = 
{
    .ChannelCount = 3U,
    .InstCfg = &Pwm_Lld_Etmr_InstCfg_Inst3,
    .ChCfgArr = Pwm_Lld_Etmr_ChCfgArr_Inst3
};

#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

