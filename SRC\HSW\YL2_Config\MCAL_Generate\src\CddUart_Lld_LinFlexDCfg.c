/**
*   @file    CddUart_Lld_LinFlexDCfg.c
*   @version 
*   @brief   Mcu Uart configure file.
*/
/*==================================================================================================
*   Project              : YTMicro AUTOSAR 4.4.0 MCAL
*   Platform             : ARM
*   Peripheral           : uart
*   Dependencies         : none
*
*   Autosar Version      : V4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : V2.2.0
*   
*
*   (c) Copyright 2020-2023 Yuntu Microelectronics co.,ltd. 
*   All Rights Reserved.
==================================================================================================*/
#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "CddUart_LinFlexD_Types.h"
#include "CddUart_Lld_LinFlexDCfg.h"
#if(CDDUART_DMA_USED == STD_ON)
#include "CddDma.h"
#endif
/*==================================================================================================
*                               FILE VERSION INFORMATION
==================================================================================================*/
#define CDDUART_VENDOR_ID_LINFLEXDUART_CFG_C               (180)
#define CDDUART_AR_REL_MAJOR_VER_LINFLEXDUART_CFG_C        (4)
#define CDDUART_AR_REL_MINOR_VER_LINFLEXDUART_CFG_C        (4)
#define CDDUART_AR_REL_REVISION_VER_LINFLEXDUART_CFG_C     (0)
#define CDDUART_SW_MAJOR_VER_LINFLEXDUART_CFG_C            (2)
#define CDDUART_SW_MINOR_VER_LINFLEXDUART_CFG_C            (2)
#define CDDUART_SW_PATCH_VER_LINFLEXDUART_CFG_C            (0)
/*==================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define CDDUART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CddUart_MemMap.h"

extern void Test_CddUart_CallBack(uint8 Channel, CddUart_General_EventType Event);/*!< Callback Function declaration */
/*==================================================================================================
*                         LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                  LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

CDDUART_CONST const LinFlexd_UartUserConfigType CddUart_Ip_HwConfig_0 = 
{
    
    .BaudRate = 115200, /*!< baud rate */
    .ParityCheck = FALSE,  /*!< parity control*/
    .ParityType = LINFLEXD_UART_PARITY_ZERO, /*!< always 0/always 1/even/odd */
    .StopBitsCount = LINFLEXD_UART_ONE_STOP_BIT, /*!< number of stop bits, 1 stop bit (default) or 2 stop bits */
    .WordLength = LINFLEXD_UART_8BITS,  /*!< number of bits per transmitted/received word */
    .TxTransferType = LINFLEXD_UART_USING_INTS, /*!< Type of UART tx transfer (interrupt/dma based) */
    .RxTransferType = LINFLEXD_UART_USING_INTS, /*!< Type of UART rx transfer (interrupt/dma based) */
    .ComplexCallback = Test_CddUart_CallBack, /*!< Callback to invoke for data transitions */
    .RxDMAChannel = 0, /*!< Channel number for DMA rx channel.*/
    .TxDMAChannel = 0, /*!< Channel number for DMA tx channel. */
    .IdleLineIntEn = FALSE, /* enable/disable Idle line interrupt */
    .IdleTimeoutValue = 0xFFFU, /*Idle line timeout value*/
};

#define CDDUART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CddUart_MemMap.h"

#ifdef __cplusplus
}
#endif

