/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file Gpt_Cfg.c
 * @brief 
 * 
 */


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Gpt_Cfg.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_CFG_C             (180)
#define GPT_AR_REL_MAJOR_VER_CFG_C      (4)
#define GPT_AR_REL_MINOR_VER_CFG_C      (4)
#define GPT_AR_REL_REVISION_VER_CFG_C   (0)
#define GPT_SW_MAJOR_VER_CFG_C          (2)
#define GPT_SW_MINOR_VER_CFG_C          (2)
#define GPT_SW_PATCH_VER_CFG_C          (0)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Gpt_Cfg.c and Gpt_Cfg.h are of the same vendor */
#if (GPT_VENDOR_ID_CFG_C != GPT_VENDOR_ID_CFG)
#error "Gpt_Cfg.c and Gpt_Cfg.h have different vendor IDs"
#endif
    /* Check if Gpt_Cfg.c and Gpt_Cfg.h are of the same Autosar version */
#if ((GPT_AR_REL_MAJOR_VER_CFG_C != GPT_AR_REL_MAJOR_VER_CFG) || \
     (GPT_AR_REL_MINOR_VER_CFG_C != GPT_AR_REL_MINOR_VER_CFG) || \
     (GPT_AR_REL_REVISION_VER_CFG_C != GPT_AR_REL_REVISION_VER_CFG) \
    )
#error "AutoSar Version Numbers of Gpt_Cfg.c and Gpt_Cfg.h are different"
#endif
/* Check if Gpt_Cfg.c and Gpt_Cfg.h are of the same software version */
#if ((GPT_SW_MAJOR_VER_CFG_C != GPT_SW_MAJOR_VER_CFG) || \
     (GPT_SW_MINOR_VER_CFG_C != GPT_SW_MINOR_VER_CFG) || \
     (GPT_SW_PATCH_VER_CFG_C != GPT_SW_PATCH_VER_CFG) \
    )
#error "Software Version Numbers of Gpt_Cfg.c and Gpt_Cfg.h are different"
#endif

#ifdef __cplusplus
}
#endif

/** @} */

